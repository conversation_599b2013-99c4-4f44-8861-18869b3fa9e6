<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Migration_Create_transaction_status_history extends CI_Migration
{
    public function up()
    {
        // Create transaction_status_history table
        $this->dbforge->add_field(array(
            'id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ),
            'transaction_id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE
            ),
            'old_status' => array(
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => TRUE
            ),
            'new_status' => array(
                'type' => 'VARCHAR',
                'constraint' => 50
            ),
            'changed_by' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE
            ),
            'changed_date' => array(
                'type' => 'DATETIME'
            ),
            'notes' => array(
                'type' => 'TEXT',
                'null' => TRUE
            )
        ));

        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_key('transaction_id');
        $this->dbforge->add_key('changed_date');
        
        $this->dbforge->create_table('transaction_status_history');

        // Add foreign key constraints if transactions table exists
        if ($this->db->table_exists('transactions')) {
            $this->db->query('ALTER TABLE transaction_status_history ADD CONSTRAINT fk_transaction_status_history_transaction FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE');
        }
    }

    public function down()
    {
        $this->dbforge->drop_table('transaction_status_history');
    }
}
