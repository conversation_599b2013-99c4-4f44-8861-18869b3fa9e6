<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MY_Model extends CI_Model
{
    protected $table = '';

    public function insert($data = array())
    {
        return $this->db->insert($this->table, $data);
    }

    public function update($where = array(), $data = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->set($data)
            ->update($this->table);
    }

    public function get($where = array())
    {
        $this->db->select('a.*')
            ->from($this->table . ' a');

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get();
    }

    public function total($where = array(), $where_in = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        if (is_array($where_in) && count($where_in) > 0) {
            foreach ($where_in as $key => $value) {
                $this->db->where_in($key, $value);
            }
        }

        return $this->db->from($this->table . ' a')->count_all_results();
    }

    public function delete($where = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->delete($this->table);
    }

    public function order_by($orderby, $direction = '')
    {
        $this->db->order_by($orderby, $direction);

        return $this;
    }

    public function limit($limit, $offset = null)
    {
        $this->db->limit($limit, $offset);

        return $this;
    }

    public function sum($column, $where = array())
    {
        $this->db->select("SUM($column) AS total")
            ->from($this->table);

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get()->row()->total;
    }

    public function like($column, $value)
    {
        $this->db->like($column, $value);

        return $this;
    }

    public function join($table, $statement, $mode = '')
    {
        $this->db->join($table, $statement, $mode);

        return $this;
    }

    public function select($column)
    {
        $this->db->select($column);

        return $this;
    }

    public function result($where = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get($this->table . ' a')->result();
    }

    public function where_in($key, $val)
    {
        if (is_array($val) && count($val) > 0) {
            $this->db->where_in($key, $val);
        }

        return $this;
    }

    public function where_not_in($key, $val)
    {
        if (is_array($val) && count($val) > 0) {
            $this->db->where_not_in($key, $val);
        }

        return $this;
    }

    public function where($key, $values = null)
    {
        $this->db->where($key, $values);

        return $this;
    }

    public function or_where($key, $values = null)
    {
        $this->db->or_where($key, $values);

        return $this;
    }

    public function group_by($by)
    {
        $this->db->group_by($by);

        return $this;
    }
}
