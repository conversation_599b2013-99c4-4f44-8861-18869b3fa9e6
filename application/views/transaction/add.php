<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Tambah Transaksi</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Transaksi / Tambah</li>
    </ul>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Tambah Transaksi</h5>
    </div>

    <div class="card-body">
        <form id="frm" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" success-redirect="<?= base_url('master/transaction') ?>">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Kode Transaksi <span class="text-danger">*</span></label>
                        <input type="text" name="transactioncode" class="form-control" placeholder="Masukkan Kode Transaksi" value="TR-<?= strtoupper(generateRandomString(10)) ?>" required readonly>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Tanggal Transaksi <span class="text-danger">*</span></label>
                        <input type="date" name="transactiondate" class="form-control" placeholder="Masukkan Tanggal Transaksi" value="<?= date('Y-m-d') ?>" required>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Keterangan Transaksi <span class="text-danger">*</span></label>
                        <input type="text" name="transactionnote" class="form-control" placeholder="Masukkan Keterangan Transaksi" value="" required>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Nominal Transaksi <span class="text-danger">*</span></label>
                        <input type="text" name="amount" class="form-control currency-input" placeholder="Masukkan Nominal Transaksi" value="" required>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Tipe Transaksi <span class="text-danger">*</span></label>
                        <select name="transactiontype" class="form-control" required>
                            <option value="">Pilih Tipe Transaksi</option>
                            <option value="Pendapatan">Pendapatan</option>
                            <option value="Pengeluaran">Pengeluaran</option>
                        </select>
                    </div>
                </div>



                <div class="col-md-3" id="hpp_field" style="display: none;">
                    <div class="mb-3">
                        <label for="" class="form-label">Modal/HPP</label>
                        <input type="text" name="hpp_amount" class="form-control currency-input" placeholder="Masukkan Modal/HPP">
                        <small class="text-muted">Opsional - hanya untuk tipe Pendapatan</small>
                    </div>
                </div>



                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Unit Usaha <span class="text-danger">*</span></label>
                        <select name="workunitid" class="form-control" required>
                            <option value="">Pilih Unit Usaha</option>
                            <?php
                            // Get available work units for current user
                            $ci = &get_instance();
                            $ci->load->model('Workunits', 'workunits');

                            if (isBumdesUser()) {
                                // Pengguna BUMDes hanya bisa pilih unit kerja mereka sendiri
                                $workunits = $ci->db->query("
                                    SELECT w.* FROM msworkunits w
                                    WHERE w.id = " . getWorkunitId() . "
                                    ORDER BY w.workunitname
                                ")->result();
                            } else {
                                // BUMDes biasa bisa pilih semua unit kerja mereka
                                $workunits = $ci->db->query("
                                    SELECT w.* FROM msworkunits w
                                    WHERE w.id IN (
                                        SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(u.workunitid, ',', numbers.n), ',', -1)) as workunit_id
                                        FROM msusers u
                                        CROSS JOIN (
                                            SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
                                        ) numbers
                                        WHERE u.id = " . getCurrentIdUser() . "
                                        AND CHAR_LENGTH(u.workunitid) - CHAR_LENGTH(REPLACE(u.workunitid, ',', '')) >= numbers.n - 1
                                    )
                                    ORDER BY w.workunitname
                                ")->result();
                            }
                            foreach ($workunits as $workunit): ?>
                                <option value="<?= $workunit->id ?>"><?= $workunit->workunitname ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" class="form-control" required>
                            <option value="Lunas">Lunas</option>
                            <option value="Menunggu Pembayaran">Menunggu Pembayaran</option>
                            <option value="Dibatalkan">Dibatalkan</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="text-end mt-3">
                <a href="<?= base_url('master/transaction') ?>" class="btn btn-danger btn-sm">Kembali</a>
                <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    window.onload = function() {
        // Mark form as having custom currency handling
        $('#frmAdd').addClass('custom-currency-handled');

        // Show/hide Modal/HPP field based on transaction type
        $('select[name=transactiontype]').change(function() {
            var type = $(this).val();
            if (type === 'Pendapatan') {
                $('#hpp_field').show();
            } else {
                $('#hpp_field').hide();
                $('input[name=hpp_amount]').val(''); // Clear value when hidden
            }
        });

        // Trigger change event on page load to set initial state
        $('select[name=transactiontype]').trigger('change');

        // Handle form submission to remove currency formatting
        $('#frmAdd').on('submit', function(e) {
            // Remove currency formatting from amount field before submission
            var amountField = $('input[name="amount"]');
            var amountValue = amountField.val().replace(/\./g, '').replace(/[^\d]/g, ''); // Remove dots and non-numeric chars
            amountField.val(amountValue);

            // Remove currency formatting from hpp_amount field if visible
            var hppField = $('input[name="hpp_amount"]');
            if (hppField.is(':visible') && hppField.val()) {
                var hppValue = hppField.val().replace(/\./g, '').replace(/[^\d]/g, ''); // Remove dots and non-numeric chars
                hppField.val(hppValue);
            }
        });
    };
</script>