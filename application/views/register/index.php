<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<section class="auth d-flex">
    <div class="auth-left">
        <div class="auth-carousel-container">
            <div class="simple-carousel" id="authCarousel">
                <div class="carousel-slide active" style="background-image: url('<?= base_url('wowdash') ?>/images/auth-images/auth-img1.png')">
                    <div class="carousel-content">
                        <h3>Ke<PERSON>la <PERSON>uangan BUMDes</h3>
                        <p>Sistem manajemen keuangan yang mudah dan terpercaya untuk BUMDes Anda</p>
                    </div>
                </div>
                <div class="carousel-slide" style="background-image: url('<?= base_url('wowdash') ?>/images/auth-images/auth-img2.png')">
                    <div class="carousel-content">
                        <h3>Laporan Real-time</h3>
                        <p>Pantau perkembangan keuangan BUMDes dengan laporan yang akurat dan real-time</p>
                    </div>
                </div>
                <div class="carousel-slide" style="background-image: url('<?= base_url('wowdash') ?>/images/auth-images/auth-img3.png')">
                    <div class="carousel-content">
                        <h3>Transparansi Penuh</h3>
                        <p>Wujudkan transparansi dan akuntabilitas dalam pengelolaan keuangan desa</p>
                    </div>
                </div>
                <div class="carousel-slide" style="background-image: url('<?= base_url('wowdash') ?>/images/auth-images/auth-img4.png')">
                    <div class="carousel-content">
                        <h3>Mudah Digunakan</h3>
                        <p>Interface yang user-friendly dan mudah dipahami oleh semua pengguna</p>
                    </div>
                </div>
                <!-- Carousel indicators -->
                <div class="carousel-indicators">
                    <span class="indicator active" data-slide="0"></span>
                    <span class="indicator" data-slide="1"></span>
                    <span class="indicator" data-slide="2"></span>
                    <span class="indicator" data-slide="3"></span>
                </div>
            </div>
        </div>
    </div>
    <div class="auth-right d-flex flex-column justify-content-center px-5">
        <div class="max-w-464-px mx-auto w-100">
            <div>
                <a href="<?= base_url() ?>" class="mb-40 max-w-290-px">
                    <img src="<?= base_url('wowdash') ?>/images/logo.png" alt="">
                </a>
                <h4 class="mb-12">Registrasi BUMDes</h4>
                <p class="mb-32 text-secondary-light text-lg">Daftarkan BUMDes Anda untuk mengakses sistem</p>
            </div>

            <form id="frmRegister" action="<?= base_url('register/process') ?>" method="POST" autocomplete="off">
                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="f7:person"></iconify-icon>
                    </span>
                    <input type="text" class="form-control h-56-px bg-neutral-50 radius-12" id="business_owner" name="business_owner" placeholder="Nama Pemilik Usaha" required>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:buildings-2-outline"></iconify-icon>
                    </span>
                    <input type="text" class="form-control h-56-px bg-neutral-50 radius-12" id="business_name" name="business_name" placeholder="Nama Usaha" required>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:map-point-wave-outline"></iconify-icon>
                    </span>
                    <textarea class="form-control bg-neutral-50 radius-12" id="address" name="address" placeholder="Alamat Lengkap" rows="3" required></textarea>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:user-outline"></iconify-icon>
                    </span>
                    <input type="text" class="form-control h-56-px bg-neutral-50 radius-12" id="username" name="username" placeholder="Username" required>
                </div>
                <span class="mt-12 text-sm text-secondary-light mb-16 d-block">Username minimal 4 karakter</span>

                <div class="mb-20">
                    <div class="position-relative">
                        <div class="icon-field">
                            <span class="icon top-50 translate-middle-y">
                                <iconify-icon icon="solar:lock-password-outline"></iconify-icon>
                            </span>
                            <input type="password" class="form-control h-56-px bg-neutral-50 radius-12" id="password" name="password" placeholder="Password" required>
                        </div>
                        <span class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light" data-toggle="#password"></span>
                    </div>
                    <span class="mt-12 text-sm text-secondary-light">Password minimal 6 karakter</span>
                </div>

                <div class="mb-20">
                    <div class="position-relative">
                        <div class="icon-field">
                            <span class="icon top-50 translate-middle-y">
                                <iconify-icon icon="solar:lock-password-outline"></iconify-icon>
                            </span>
                            <input type="password" class="form-control h-56-px bg-neutral-50 radius-12" id="confirm_password" name="confirm_password" placeholder="Konfirmasi Password" required>
                        </div>
                        <span class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light" data-toggle="#confirm_password"></span>
                    </div>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:map-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="province_id" name="province_id" required>
                        <option value="">Pilih Provinsi</option>
                        <?php foreach ($provinces as $province): ?>
                            <option value="<?= $province->code ?>"><?= $province->name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:city-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="city_id" name="city_id" required>
                        <option value="">Pilih Kabupaten/Kota</option>
                    </select>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:map-point-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="district_id" name="district_id" required>
                        <option value="">Pilih Kecamatan</option>
                    </select>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:home-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="village_id" name="village_id" required>
                        <option value="">Pilih Desa</option>
                    </select>
                </div>

                <div class="mb-16">
                    <label class="form-label mb-8">Unit Usaha</label>
                    <select class="form-control" id="workunit_ids" name="workunit_ids[]" multiple required>
                        <?php foreach ($workunits as $workunit): ?>
                            <option value="<?= $workunit->id ?>"><?= $workunit->workunitname ?></option>
                        <?php endforeach; ?>
                    </select>
                    <span class="mt-8 text-sm text-secondary-light d-block">Pilih unit usaha yang akan dikelola (dapat memilih lebih dari satu)</span>
                </div>

                <div class="mb-20">
                    <div class="form-check style-check d-flex align-items-start">
                        <input class="form-check-input border border-neutral-300 mt-4" type="checkbox" id="agree_terms" required>
                        <label class="form-check-label text-sm" for="agree_terms">
                            Saya setuju dengan syarat dan ketentuan yang berlaku
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mt-32">
                    Daftar Sekarang
                </button>
            </form>

            <div class="mt-32 center-border-horizontal text-center">
                <span class="bg-base z-1 px-4">Sudah punya akun?</span>
            </div>
            <a href="<?= base_url('auth/login') ?>" class="btn btn-outline-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mt-32">Login di sini</a>
        </div>
    </div>
</section>

<style>
    .icon-field .form-control {
        padding-top: 15px !important;
    }

    .icon-field textarea.form-control {
        padding-top: 15px !important;
        padding-left: 45px !important;
        resize: vertical;
    }

    .select2 .selection {
        width: 100%;
    }

    .select2-container--default .select2-selection--multiple {
        background-color: #f8f9fa !important;
        border: 1px solid #e0e6ed !important;
        border-radius: 12px !important;
        min-height: 56px !important;
        padding: 8px 12px !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #487fff !important;
        border: 1px solid #487fff !important;
        border-radius: 6px !important;
        color: white !important;
        padding: 4px 8px !important;
        margin: 2px !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: white !important;
        margin-right: 5px !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #ff6b6b !important;
    }

    .select2-dropdown {
        border-radius: 8px !important;
        border: 1px solid #e0e6ed !important;
    }

    /* Auth Carousel Styles */
    .auth-carousel-container {
        position: relative;
        max-width: 500px;
        margin: 0 auto;
    }

    .auth-carousel {
        position: relative;
    }

    .carousel-slide {
        text-align: center;
        padding: 20px;
    }

    .carousel-image {
        width: 100%;
        height: 250px;
        object-fit: cover;
        margin-bottom: 24px;
        border-radius: 12px;
    }

    .carousel-content h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 16px;
    }

    .carousel-content p {
        font-size: 1rem;
        line-height: 1.6;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Slick Carousel Custom Styles */
    .auth-carousel .slick-dots {
        bottom: -50px;
    }

    .auth-carousel .slick-dots li button:before {
        color: #487fff;
        font-size: 12px;
    }

    .auth-carousel .slick-dots li.slick-active button:before {
        color: #487fff;
        opacity: 1;
    }

    .auth-carousel .slick-arrow {
        z-index: 2;
    }

    .auth-carousel .slick-prev {
        left: -40px;
    }

    .auth-carousel .slick-next {
        right: -40px;
    }

    .auth-carousel .slick-prev:before,
    .auth-carousel .slick-next:before {
        color: #487fff;
        font-size: 20px;
    }

    @media (max-width: 1200px) {
        .auth-carousel .slick-prev {
            left: -30px;
        }

        .auth-carousel .slick-next {
            right: -30px;
        }
    }
</style>

<script>
    // Wait for jQuery to be available
    function waitForJQuery() {
        if (typeof $ === 'undefined') {
            setTimeout(waitForJQuery, 50);
            return;
        }

        $(document).ready(function() {
            // Debug: Check if SweetAlert is loaded
            console.log('SweetAlert available:', typeof swal !== 'undefined');
            console.log('SweetAlert functions available:', {
                swalError: typeof swalError !== 'undefined',
                swalMessageSuccess: typeof swalMessageSuccess !== 'undefined',
                swalMessageFailed: typeof swalMessageFailed !== 'undefined'
            });

            // ================== Auth Carousel Js Start ==========
            $('.auth-carousel').slick({
                dots: true,
                infinite: true,
                speed: 500,
                fade: true,
                cssEase: 'linear',
                autoplay: true,
                autoplaySpeed: 4000,
                arrows: true,
                pauseOnHover: true,
                pauseOnFocus: true
            });
            // ========================= Auth Carousel Js End ===========================

            // Wait for Select2 to be available
            setTimeout(function() {
                // Initialize Select2 for work units
                if (typeof $.fn.select2 !== 'undefined') {
                    $('#workunit_ids').select2({
                        placeholder: 'Pilih unit usaha',
                        allowClear: true,
                        width: '100%',
                        theme: 'default'
                    });
                } else {
                    console.error('Select2 not loaded');
                }
            }, 100);

            // Province change event
            $('#province_id').change(function() {
                const provinceCode = $(this).val();
                $('#city_id').html('<option value="">Pilih Kabupaten/Kota</option>');
                $('#district_id').html('<option value="">Pilih Kecamatan</option>');
                $('#village_id').html('<option value="">Pilih Desa</option>');

                if (provinceCode) {
                    loadCities(provinceCode);
                }
            });

            // City change event
            $('#city_id').change(function() {
                const cityCode = $(this).val();
                $('#district_id').html('<option value="">Pilih Kecamatan</option>');
                $('#village_id').html('<option value="">Pilih Desa</option>');

                if (cityCode) {
                    loadDistricts(cityCode);
                }
            });

            // District change event
            $('#district_id').change(function() {
                const districtCode = $(this).val();
                $('#village_id').html('<option value="">Pilih Desa</option>');

                if (districtCode) {
                    loadVillages(districtCode);
                }
            });

            // Form submission
            $.AjaxRequest('#frmRegister', {
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        return swalMessageSuccess(response.MESSAGE, ok => {
                            return window.location.href = '<?= base_url('auth/login') ?>';
                        })
                    } else {
                        return swalMessageFailed(response.MESSAGE);
                    }
                },
                error: function() {
                    return swalError();
                }
            });
        });

        function loadCities(provinceCode) {
            $.post('<?= base_url('register/get_cities') ?>', {
                province_code: provinceCode
            }, function(response) {
                let options = '<option value="">Pilih Kabupaten/Kota</option>';
                response.forEach(function(city) {
                    options += `<option value="${city.code}">${city.name}</option>`;
                });
                $('#city_id').html(options);
            });
        }

        function loadDistricts(cityCode) {
            $.post('<?= base_url('register/get_districts') ?>', {
                city_code: cityCode
            }, function(response) {
                let options = '<option value="">Pilih Kecamatan</option>';
                response.forEach(function(district) {
                    options += `<option value="${district.code}">${district.name}</option>`;
                });
                $('#district_id').html(options);
            });
        }

        function loadVillages(districtCode) {
            $.post('<?= base_url('register/get_villages') ?>', {
                district_code: districtCode
            }, function(response) {
                let options = '<option value="">Pilih Desa</option>';
                response.forEach(function(village) {
                    options += `<option value="${village.code}">${village.name}</option>`;
                });
                $('#village_id').html(options);
            });
        }
    };

    // Start the initialization
    waitForJQuery();
</script>

<style>
    /* Full Height Carousel Styles */
    .auth {
        min-height: 100vh;
    }

    .auth-left {
        width: 50%;
        height: 100vh;
        position: relative;
    }

    .auth-right {
        width: 50%;
        height: 100vh;
        overflow-y: auto;
    }

    .auth-carousel-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .simple-carousel {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .carousel-slide {
        display: none;
        text-align: center;
        padding: 80px 60px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        color: white;
        height: 100%;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .carousel-slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1;
    }

    .carousel-slide.active {
        display: flex;
    }

    .carousel-content {
        position: relative;
        z-index: 2;
    }

    .carousel-content h3 {
        font-size: 36px;
        font-weight: 700;
        margin-bottom: 20px;
        color: #ffffff;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .carousel-content p {
        font-size: 20px;
        line-height: 1.6;
        color: #ffffff;
        max-width: 450px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        margin: auto;
    }

    /* Carousel Indicators */
    .carousel-indicators {
        position: absolute;
        bottom: 40px;
        display: flex;
        gap: 12px;
        z-index: 3;
    }

    .indicator {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid rgba(255, 255, 255, 0.8);
    }

    .indicator.active {
        background: #ffffff;
        transform: scale(1.3);
        border: 2px solid #487fff;
    }

    .indicator:hover {
        background: rgba(255, 255, 255, 0.8);
    }

    @media (max-width: 992px) {
        .auth-left {
            display: none !important;
        }

        .auth-right {
            width: 100%;
        }
    }

    @media (max-width: 768px) {
        .carousel-slide {
            padding: 60px 40px;
        }

        .carousel-content h3 {
            font-size: 28px;
        }

        .carousel-content p {
            font-size: 16px;
        }
    }
</style>

<script>
    // ================== Simple Carousel Js Start ==========
    $(document).ready(function() {
        let currentSlide = 0;
        const slides = $('.carousel-slide');
        const indicators = $('.indicator');
        const totalSlides = slides.length;

        function showSlide(index) {
            slides.removeClass('active');
            indicators.removeClass('active');

            slides.eq(index).addClass('active');
            indicators.eq(index).addClass('active');
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        // Auto-play carousel
        setInterval(nextSlide, 4000);

        // Indicator click handlers
        indicators.on('click', function() {
            currentSlide = $(this).data('slide');
            showSlide(currentSlide);
        });
    });
    // ========================= Simple Carousel Js End ===========================
</script>