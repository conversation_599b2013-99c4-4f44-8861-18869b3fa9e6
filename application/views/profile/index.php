<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Profil Pengguna</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Profil</li>
    </ul>
</div>

<div class="row gy-4">
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex flex-column align-items-center">
                    <div class="avatar-upload mb-24">
                        <div class="avatar-preview">
                            <div class="rounded-circle bg-primary-50 d-flex align-items-center justify-content-center">
                                <iconify-icon icon="solar:user-bold" class="text-primary-600 text-6xl"></iconify-icon>
                            </div>
                        </div>
                    </div>

                    <h5 class="mb-8"><?= $user->name ?></h5>
                    <span class="text-secondary-light mb-16"><?= getSessionValue('ROLE') ?></span>

                    <?php if (isBumdesUser()): ?>
                        <div class="text-center">
                            <p class="mb-4"><strong>BUMDes:</strong> <?= $user->bumdes_name ?? 'N/A' ?></p>
                            <p class="mb-4"><strong>Unit Usaha:</strong> <?= $user->workunitname ?? 'N/A' ?></p>
                        </div>
                    <?php elseif (isBumdes()): ?>
                        <div class="text-center">
                            <p class="mb-4"><strong>Nama Usaha:</strong> <?= $user->businessname ?? 'N/A' ?></p>
                        </div>
                    <?php endif; ?>

                    <a href="<?= base_url('profile/change_password') ?>" class="btn btn-primary btn-sm d-flex gap-1 align-items-center">
                        <iconify-icon icon="solar:lock-password-outline" class="icon text-lg"></iconify-icon>
                        Ubah Password
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Profil</h5>
            </div>
            <div class="card-body">
                <form id="frmProfile" action="<?= base_url('profile/update_profile') ?>" method="POST" autocomplete="off">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label fw-semibold text-primary-light text-sm mb-8">Nama Lengkap <span class="text-danger">*</span></label>
                                <input type="text" class="form-control radius-8" id="name" name="name" value="<?= $user->name ?>" placeholder="Masukkan nama lengkap" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label fw-semibold text-primary-light text-sm mb-8">Username</label>
                                <input type="text" class="form-control radius-8" id="username" value="<?= $user->username ?>" placeholder="Username" readonly>
                                <small class="text-muted">Username tidak dapat diubah</small>
                            </div>
                        </div>
                    </div>

                    <?php if (isBumdesUser() || (isset($user->phone) || isset($user->address))): ?>
                        <div class="row">
                            <?php if (isBumdesUser() || isset($user->phone)): ?>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label fw-semibold text-primary-light text-sm mb-8">No. Telepon</label>
                                        <input type="text" class="form-control radius-8" id="phone" name="phone" value="<?= $user->phone ?? '' ?>" placeholder="Masukkan nomor telepon">
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (isBumdesUser() || isset($user->address)): ?>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="address" class="form-label fw-semibold text-primary-light text-sm mb-8">Alamat</label>
                                        <textarea class="form-control radius-8" id="address" name="address" rows="3" placeholder="Masukkan alamat"><?= $user->address ?? '' ?></textarea>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Role</label>
                                <input type="text" class="form-control radius-8" value="<?= getSessionValue('ROLE') ?>" readonly>
                            </div>
                        </div>
                        <?php if (isBumdesUser()): ?>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold text-primary-light text-sm mb-8">Status</label>
                                    <input type="text" class="form-control radius-8" value="<?= $user->status ?? 'Aktif' ?>" readonly>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="d-flex align-items-center justify-content-end gap-3">
                        <button type="button" class="btn btn-outline-primary border radius-8 px-20 py-11" onclick="window.location.reload()">Reset</button>
                        <button type="submit" class="btn btn-primary border border-primary-600 text-md px-24 py-12 radius-8">
                            Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <?php if (isBumdes()): ?>
            <!-- Business Information Card -->
            <div class="card" style="margin-top: 1rem;">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Usaha</h5>
                </div>
                <div class="card-body">
                    <form id="frmBusinessInfo" action="<?= base_url('profile/update_business_info') ?>" method="POST" autocomplete="off">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_owner" class="form-label fw-semibold text-primary-light text-sm mb-8">Nama Pemilik Usaha <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control radius-8" id="business_owner" name="business_owner" value="<?= $user->name ?>" placeholder="Masukkan nama pemilik usaha" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_name" class="form-label fw-semibold text-primary-light text-sm mb-8">Nama Usaha <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control radius-8" id="business_name" name="business_name" value="<?= $user->businessname ?>" placeholder="Masukkan nama usaha" required>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-end gap-3">
                            <button type="button" class="btn btn-outline-primary border radius-8 px-20 py-11" onclick="window.location.reload()">Reset</button>
                            <button type="submit" class="btn btn-primary border border-primary-600 text-md px-24 py-12 radius-8">
                                Simpan Informasi Usaha
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmProfile', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.reload();
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        <?php if (isBumdes()): ?>
            $.AjaxRequest('#frmBusinessInfo', {
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        return swalMessageSuccess(response.MESSAGE, ok => {
                            return window.location.reload();
                        })
                    } else {
                        return swalMessageFailed(response.MESSAGE);
                    }
                },
                error: function() {
                    return swalError();
                }
            });
        <?php endif; ?>
    };
</script>