<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Ubah Password</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">
            <a href="<?= base_url('profile') ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                Profil
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Ubah Password</li>
    </ul>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 d-flex align-items-center gap-2">
                    <iconify-icon icon="solar:lock-password-outline" class="icon text-lg me-2"></iconify-icon>
                    Ubah Password
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info d-flex align-items-center gap-2" role="alert">
                    <iconify-icon icon="solar:info-circle-outline" class="icon text-lg me-2"></iconify-icon>
                    <div>
                        <strong>Perhatian:</strong> Password baru harus minimal 6 karakter untuk keamanan akun Anda.
                    </div>
                </div>

                <form id="frmChangePassword" action="<?= base_url('profile/process_change_password') ?>" method="POST" autocomplete="off">
                    <div class="mb-20">
                        <label for="current_password" class="form-label fw-semibold text-primary-light text-sm mb-8">
                            Password Saat Ini <span class="text-danger">*</span>
                        </label>
                        <div class="position-relative">
                            <input type="password" class="form-control radius-8" id="current_password" name="current_password" placeholder="Masukkan password saat ini" required>
                            <span class="position-absolute top-50 translate-middle-y end-0 me-12 cursor-pointer" onclick="togglePassword('current_password')">
                                <iconify-icon icon="solar:eye-outline" class="icon text-lg" id="current_password_icon"></iconify-icon>
                            </span>
                        </div>
                    </div>

                    <div class="mb-20">
                        <label for="new_password" class="form-label fw-semibold text-primary-light text-sm mb-8">
                            Password Baru <span class="text-danger">*</span>
                        </label>
                        <div class="position-relative">
                            <input type="password" class="form-control radius-8" id="new_password" name="new_password" placeholder="Masukkan password baru" required minlength="6">
                            <span class="position-absolute top-50 translate-middle-y end-0 me-12 cursor-pointer" onclick="togglePassword('new_password')">
                                <iconify-icon icon="solar:eye-outline" class="icon text-lg" id="new_password_icon"></iconify-icon>
                            </span>
                        </div>
                        <small class="text-muted">Minimal 6 karakter</small>
                    </div>

                    <div class="mb-20">
                        <label for="confirm_password" class="form-label fw-semibold text-primary-light text-sm mb-8">
                            Konfirmasi Password Baru <span class="text-danger">*</span>
                        </label>
                        <div class="position-relative">
                            <input type="password" class="form-control radius-8" id="confirm_password" name="confirm_password" placeholder="Konfirmasi password baru" required minlength="6">
                            <span class="position-absolute top-50 translate-middle-y end-0 me-12 cursor-pointer" onclick="togglePassword('confirm_password')">
                                <iconify-icon icon="solar:eye-outline" class="icon text-lg" id="confirm_password_icon"></iconify-icon>
                            </span>
                        </div>
                        <small class="text-muted">Harus sama dengan password baru</small>
                    </div>

                    <div class="d-flex align-items-center justify-content-between gap-3">
                        <a href="<?= base_url('profile') ?>" class="btn btn-outline-primary border radius-8 px-20 py-11 d-flex gap-1 align-items-center">
                            <iconify-icon icon="solar:arrow-left-outline" class="icon text-lg me-1"></iconify-icon>
                            Kembali
                        </a>
                        <button type="submit" class="btn btn-primary border border-primary-600 text-md px-24 py-12 radius-8 d-flex gap-1 align-items-center">
                            <iconify-icon icon="solar:diskette-outline" class="icon text-lg me-1"></iconify-icon>
                            Ubah Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <!-- Password Strength Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0 d-flex align-items-center gap-2">
                    <iconify-icon icon="solar:shield-check-outline" class="icon text-lg me-2"></iconify-icon>
                    Tips Keamanan Password
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="d-flex align-items-center mb-2 gap-1">
                        <iconify-icon icon="solar:check-circle-outline" class="icon text-success me-2"></iconify-icon>
                        Gunakan minimal 6 karakter
                    </li>
                    <li class="d-flex align-items-center mb-2 gap-1">
                        <iconify-icon icon="solar:check-circle-outline" class="icon text-success me-2"></iconify-icon>
                        Kombinasikan huruf besar dan kecil
                    </li>
                    <li class="d-flex align-items-center mb-2 gap-1">
                        <iconify-icon icon="solar:check-circle-outline" class="icon text-success me-2"></iconify-icon>
                        Tambahkan angka dan simbol
                    </li>
                    <li class="d-flex align-items-center gap-1">
                        <iconify-icon icon="solar:check-circle-outline" class="icon text-success me-2"></iconify-icon>
                        Jangan gunakan informasi pribadi
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + '_icon');

        if (field.type === 'password') {
            field.type = 'text';
            icon.setAttribute('icon', 'solar:eye-closed-outline');
        } else {
            field.type = 'password';
            icon.setAttribute('icon', 'solar:eye-outline');
        }
    }

    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = this.value;

        if (confirmPassword && newPassword !== confirmPassword) {
            this.setCustomValidity('Password tidak sama');
        } else {
            this.setCustomValidity('');
        }
    });

    window.onload = function() {
        $.AjaxRequest('#frmChangePassword', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = '<?= base_url('profile') ?>';
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    };
</script>