<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelolakas - Transformasi Digital untuk Keuangan Desa</title>

    <link rel="icon" type="image/png" href="<?= base_url('wowdash') ?>/images/favicon.png" sizes="16x16">

    <!-- remix icon font css  -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/remixicon.css">

    <!-- BootStrap css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/bootstrap.min.css">

    <!-- Apex Chart css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/apexcharts.css">

    <!-- Data Table css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/dataTables.min.css">

    <!-- Text Editor css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/editor-katex.min.css">
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/editor.atom-one-dark.min.css">
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/editor.quill.snow.css">

    <!-- Date picker css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/flatpickr.min.css">

    <!-- Calendar css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/full-calendar.css">

    <!-- Vector Map css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/jquery-jvectormap-2.0.5.css">

    <!-- Popup css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/magnific-popup.css">

    <!-- Slick Slider css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/slick.css">

    <!-- prism css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/prism.css">

    <!-- file upload css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/file-upload.css">
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/audioplayer.css">

    <!-- main css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/style.css">
</head>

<body>
    <section class="auth bg-base d-flex flex-wrap">
        <div class="auth-left d-lg-block d-none">
            <div class="d-flex align-items-center flex-column h-100 justify-content-center">
                <div class="auth-carousel-container w-100 h-100 d-flex align-items-center justify-content-center">
                    <div class="simple-carousel" id="authCarousel">
                        <div class="carousel-slide active">
                            <img src="<?= base_url('wowdash') ?>/images/auth-images/auth-img1.png" alt="Slide 1" class="carousel-image">
                            <div class="carousel-content">
                                <h3 class="text-primary-600 mb-16">Kelola Keuangan BUMDes</h3>
                                <p class="text-secondary-light">Sistem manajemen keuangan yang mudah dan terpercaya untuk BUMDes Anda</p>
                            </div>
                        </div>
                        <div class="carousel-slide">
                            <img src="<?= base_url('wowdash') ?>/images/auth-images/auth-img2.png" alt="Slide 2" class="carousel-image">
                            <div class="carousel-content">
                                <h3 class="text-primary-600 mb-16">Laporan Real-time</h3>
                                <p class="text-secondary-light">Pantau perkembangan keuangan BUMDes dengan laporan yang akurat dan real-time</p>
                            </div>
                        </div>
                        <div class="carousel-slide">
                            <img src="<?= base_url('wowdash') ?>/images/auth-images/auth-img3.png" alt="Slide 3" class="carousel-image">
                            <div class="carousel-content">
                                <h3 class="text-primary-600 mb-16">Transparansi Penuh</h3>
                                <p class="text-secondary-light">Wujudkan transparansi dan akuntabilitas dalam pengelolaan keuangan desa</p>
                            </div>
                        </div>
                        <div class="carousel-slide">
                            <img src="<?= base_url('wowdash') ?>/images/auth-images/auth-img4.png" alt="Slide 4" class="carousel-image">
                            <div class="carousel-content">
                                <h3 class="text-primary-600 mb-16">Mudah Digunakan</h3>
                                <p class="text-secondary-light">Interface yang user-friendly dan mudah dipahami oleh semua pengguna</p>
                            </div>
                        </div>
                        <!-- Carousel indicators -->
                        <div class="carousel-indicators">
                            <span class="indicator active" data-slide="0"></span>
                            <span class="indicator" data-slide="1"></span>
                            <span class="indicator" data-slide="2"></span>
                            <span class="indicator" data-slide="3"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="auth-right py-32 px-24 d-flex flex-column justify-content-center">
            <div class="max-w-464-px mx-auto w-100">
                <div>
                    <a href="<?= base_url() ?>" class="mb-40 max-w-290-px">
                        <img src="<?= base_url('wowdash') ?>/images/logo.png" alt="">
                    </a>

                    <h4 class="mb-12">Login ke Akun Anda</h4>
                    <p class="mb-32 text-secondary-light text-lg">Selamat datang kembali! Silahkan isi formulir dengan data yang telah terdaftar.</p>
                </div>

                <form id="frmLogin" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <div class="icon-field mb-16">
                        <span class="icon top-50 translate-middle-y">
                            <iconify-icon icon="mage:user"></iconify-icon>
                        </span>

                        <input type="text" name="username" class="form-control h-56-px bg-neutral-50 radius-12" placeholder="Username" required autofocus>
                    </div>

                    <div class="position-relative mb-20">
                        <div class="icon-field">
                            <span class="icon top-50 translate-middle-y">
                                <iconify-icon icon="solar:lock-password-outline"></iconify-icon>
                            </span>

                            <input type="password" name="password" class="form-control h-56-px bg-neutral-50 radius-12" id="your-password" placeholder="Password" required>
                        </div>

                        <span class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light" data-toggle="#your-password"></span>
                    </div>

                    <button type="submit" class="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mt-32 mb-8">Log in</button>
                </form>

                <div class="text-center mt-3">
                    <p class="mb-0 text-secondary-light">Belum punya akun BUMDes? <a href="<?= base_url('register') ?>" class="text-primary fw-semibold">Daftar di sini</a></p>
                </div>
            </div>
        </div>
    </section>

    <!-- jQuery library js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/bootstrap.bundle.min.js"></script>

    <!-- Apex Chart js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/apexcharts.min.js"></script>

    <!-- Data Table js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/dataTables.min.js"></script>

    <!-- Iconify Font js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/iconify-icon.min.js"></script>

    <!-- jQuery UI js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-ui.min.js"></script>

    <!-- Vector Map js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-jvectormap-2.0.5.min.js"></script>
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-jvectormap-world-mill-en.js"></script>

    <!-- Popup js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/magnifc-popup.min.js"></script>

    <!-- Slick Slider js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/slick.min.js"></script>

    <!-- prism js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/prism.js"></script>

    <!-- file upload js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/file-upload.js"></script>

    <!-- audioplayer -->
    <script src="<?= base_url('wowdash') ?>/js/lib/audioplayer.js"></script>

    <!-- main js -->
    <script src="<?= base_url('wowdash') ?>/js/app.js"></script>

    <!-- helpers -->
    <script src="<?= base_url() ?>node_modules/sweetalert/dist/sweetalert.min.js"></script>
    <script src="<?= base_url('assets/js/ajax-request.js') ?>"></script>
    <script src="<?= base_url('assets/js/script.js') ?>"></script>

    <style>
        /* Simple Carousel Styles */
        .auth-carousel-container {
            position: relative;
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .simple-carousel {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .carousel-slide {
            display: none;
            text-align: center;
            padding: 60px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 400px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .carousel-slide.active {
            display: flex;
        }

        .carousel-image {
            max-width: 200px;
            height: auto;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .carousel-content h3 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #ffffff;
        }

        .carousel-content p {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            max-width: 400px;
        }

        /* Carousel Indicators */
        .carousel-indicators {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: #ffffff;
            transform: scale(1.2);
        }

        .indicator:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        @media (max-width: 768px) {
            .carousel-slide {
                padding: 40px 20px;
                min-height: 350px;
            }

            .carousel-content h3 {
                font-size: 24px;
            }

            .carousel-image {
                max-width: 150px;
            }
        }
    </style>

    <script>
        // ================== Password Show Hide Js Start ==========
        function initializePasswordToggle(toggleSelector) {
            $(toggleSelector).on('click', function() {
                $(this).toggleClass("ri-eye-off-line");
                var input = $($(this).attr("data-toggle"));
                if (input.attr("type") === "password") {
                    input.attr("type", "text");
                } else {
                    input.attr("type", "password");
                }
            });
        }

        // Call the function
        initializePasswordToggle('.toggle-password');
        // ========================= Password Show Hide Js End ===========================

        // ================== Simple Carousel Js Start ==========
        $(document).ready(function() {
            let currentSlide = 0;
            const slides = $('.carousel-slide');
            const indicators = $('.indicator');
            const totalSlides = slides.length;

            function showSlide(index) {
                slides.removeClass('active');
                indicators.removeClass('active');

                slides.eq(index).addClass('active');
                indicators.eq(index).addClass('active');
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }

            // Auto-play carousel
            setInterval(nextSlide, 4000);

            // Indicator click handlers
            indicators.on('click', function() {
                currentSlide = $(this).data('slide');
                showSlide(currentSlide);
            });
        });
        // ========================= Simple Carousel Js End ===========================

        $.AjaxRequest('#frmLogin', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.reload();
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    </script>

</body>

</html>