<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Lapor<PERSON></h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Laporan <PERSON></li>
    </ul>
</div>

<div class="row gy-4">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-3">
                <label for="year" class="form-label"><PERSON>hun</label>
                <input type="number" class="form-control" id="year" name="year" value="<?= $year ?>" min="2020" max="<?= date('Y') + 1 ?>">
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <div class="card basic-data-table">
            <div class="card-header">
                <h5 class="card-title mb-0">Ringkasan Saldo per Unit Kerja</h5>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table bordered-table datatables">
                        <thead>
                            <tr>
                                <th>Unit Kerja</th>
                                <th>Saldo Awal</th>
                                <th>Total Pendapatan</th>
                                <th>Total Pengeluaran</th>
                                <th>Saldo Akhir</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($beginningbalances as $key => $value): ?>
                                <tr>
                                    <td><?= $value->workunitcode . ' - ' . $value->workunitname ?></td>
                                    <td>Rp <?= IDR($value->beginning_balances) ?></td>
                                    <td>Rp <?= IDR($value->total_income) ?></td>
                                    <td>Rp <?= IDR($value->total_expense) ?></td>
                                    <td>Rp <?= IDR($value->beginning_balances + $value->total_income - $value->total_expense) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <div class="card basic-data-table">
            <div class="card-header">
                <h5 class="card-title mb-0">Daftar Detail Laporan</h5>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table bordered-table datatables">
                        <thead>
                            <tr>
                                <th>Tanggal</th>
                                <th>Keterangan</th>
                                <th>Debit</th>
                                <th>Kredit</th>
                                <th>Saldo</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr>
                                <td>
                                    <strong>Total Saldo Awal</strong>
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <strong>Rp <?= IDR($beginningbalance) ?></strong>
                                </td>
                            </tr>

                            <?php foreach ($transactions as $key => $value): ?>
                                <tr>
                                    <td><?= tgl_indo($value->transactiondate) ?></td>
                                    <td><?= $value->transactionnote ?></td>
                                    <td>
                                        <?php if ($value->transactiontype == 'Pendapatan'): ?>
                                            Rp <?= IDR($value->amount) ?>

                                            <?php $beginningbalance += $value->amount; ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($value->transactiontype == 'Pengeluaran'): ?>
                                            Rp <?= IDR($value->amount) ?>

                                            <?php $beginningbalance -= $value->amount; ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        Rp <?= IDR($beginningbalance) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>

                            <tr>
                                <td>
                                    <strong>Total Saldo Akhir</strong>
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <strong>Rp <?= IDR($beginningbalance) ?></strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('#year').change(function() {
            let year = $(this).val();
            window.location.href = '<?= base_url('report/balance') ?>?year=' + year;
        });
    }
</script>