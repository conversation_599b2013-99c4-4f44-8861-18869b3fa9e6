<?php
defined('BASEPATH') or die('No direct script access allowed!');
// Broadcast WhatsApp feature temporarily disabled
return;
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Broadcast WhatsApp</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Broadcast WhatsApp</li>
    </ul>
</div>

<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Formulir Broadcast WhatsApp</h5>
            </div>

            <div class="card-body">
                <form id="frmBroadcast" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <div class="mb-3">
                        <label for="target" class="form-label">Tujuan</label>
                        <select name="target[]" id="target" class="form-control" multiple required>
                            <?php foreach ($customers as $customer) : ?>
                                <option value="<?= $customer->id ?>"><?= $customer->name ?> (<?= $customer->phonenumber ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="messages" class="form-label">Pesan</label>
                        <textarea name="messages" class="form-control" placeholder="Masukkan pesan" rows="5" required></textarea>
                    </div>

                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-success btn-sm">Broadcast</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <div class="card basic-data-table">
            <div class="card-header">
                <h5 class="card-title mb-0">Riwayat Broadcast</h5>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table bordered-table datatables">
                        <thead>
                            <tr>
                                <th>Tanggal Broadcast</th>
                                <th>Kode Broadcast</th>
                                <th>Pesan</th>
                                <th>Progress</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($broadcasts as $key => $value): ?>
                                <?php
                                $total_all = $value->total_all;
                                $total_processed = $value->total_processed;
                                $total_success = $value->total_success;

                                $progress = 0;
                                if ($total_all > 0) {
                                    $progress = ($total_processed / $total_all) * 100;
                                }
                                ?>
                                <tr>
                                    <td><?= tgl_indo(date('Y-m-d', strtotime($value->createddate))) ?> <?= date('H:i:s', strtotime($value->createddate)) ?></td>
                                    <td><?= $value->broadcastcode ?></td>
                                    <td><?= $value->messages ?></td>
                                    <td>
                                        <div class="progress h-8-px w-100 bg-primary-50" role="progressbar" aria-label="Basic example" aria-valuenow="<?= $progress ?>" aria-valuemin="0" aria-valuemax="100">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated rounded-pill bg-primary-600" style="width: <?= $progress ?>%"></div>
                                        </div>

                                        <small class="text-secondary-light"><?= $total_success ?> dari <?= $total_all ?> terkirim</small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('#target').select2();

        $.AjaxRequest('#frmBroadcast', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.reload();
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    };
</script>