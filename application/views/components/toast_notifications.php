<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- Toast Notifications Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    <!-- Success Toast -->
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <iconify-icon icon="solar:check-circle-bold" class="me-2"></iconify-icon>
                <span class="toast-message">Operasi berhasil!</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Error Toast -->
    <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <iconify-icon icon="solar:close-circle-bold" class="me-2"></iconify-icon>
                <span class="toast-message">Terjadi kesalahan!</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Warning Toast -->
    <div id="warningToast" class="toast align-items-center text-white bg-warning border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <iconify-icon icon="solar:danger-triangle-bold" class="me-2"></iconify-icon>
                <span class="toast-message">Peringatan!</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Info Toast -->
    <div id="infoToast" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <iconify-icon icon="solar:info-circle-bold" class="me-2"></iconify-icon>
                <span class="toast-message">Informasi</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
// Toast notification functions
function showToast(type, message, duration = 5000) {
    var toastId = type + 'Toast';
    var toastElement = document.getElementById(toastId);
    
    if (toastElement) {
        // Update message
        var messageElement = toastElement.querySelector('.toast-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        // Show toast
        var toast = new bootstrap.Toast(toastElement, {
            delay: duration
        });
        toast.show();
    }
}

function showSuccessToast(message, duration = 5000) {
    showToast('success', message, duration);
}

function showErrorToast(message, duration = 5000) {
    showToast('error', message, duration);
}

function showWarningToast(message, duration = 5000) {
    showToast('warning', message, duration);
}

function showInfoToast(message, duration = 5000) {
    showToast('info', message, duration);
}

// Auto-hide toasts after specified duration
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all toasts
    var toastElements = document.querySelectorAll('.toast');
    toastElements.forEach(function(toastElement) {
        new bootstrap.Toast(toastElement);
    });
});
</script>

<style>
.toast-container .toast {
    margin-bottom: 0.5rem;
}

.toast-body {
    display: flex;
    align-items: center;
}

.toast-body iconify-icon {
    font-size: 1.2rem;
}

/* Custom animations */
.toast.showing {
    animation: slideInRight 0.3s ease-out;
}

.toast.hide {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
</style>
