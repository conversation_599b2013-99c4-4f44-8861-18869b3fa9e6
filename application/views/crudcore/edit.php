<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0"><?= $title ?></h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">
            <?= isset($element['title']) ? $element['title'] : explode(' ', $title)[1] ?> / Ubah
        </li>
    </ul>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0"><?= isset($element['title']) ? $element['title'] : 'Formulir ' . $title ?></h5>
    </div>

    <div class="card-body">
        <form id="frm" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" success-redirect="<?= isset($element['success-redirect']) ? $element['success-redirect'] : base_url('master/' . $feature) ?>">
            <?php foreach ($element['fields'] as $key => $value) : ?>
                <?php
                $attr = isset($value['attr']) ? $value['attr'] : array();

                $attribute = "";
                foreach ($attr as $k => $v) {
                    $attribute .= "$k='$v' ";
                }
                ?>

                <?php if ($value['type'] != 'row') : ?>
                    <div class="<?= isset($value['input_type']) ? ($value['input_type'] == 'hidden' ? 'd-none' : 'mb-3') : 'mb-3' ?>">
                        <?php if (isset($value['label'])) : ?>
                            <label for="" class="form-label"><?= $value['label'] ?> <?= isset($value['required']) && $value['required'] == true ? '<span class="text-danger">*</span>' : null ?></label>
                        <?php endif; ?>

                        <?php if ($value['type'] == 'input') : ?>
                            <?php $variable = $value['variable']; ?>

                            <?php if (str_contains($variable, 'validated')) : ?>
                                <?php $variable = explode('_', $variable)[1]; ?>
                            <?php elseif (str_contains($variable, 'required')) : ?>
                                <?php
                                $variable = explode('_', $variable);
                                array_shift($variable);

                                $variable = implode('_', $variable);
                                ?>
                            <?php endif; ?>

                            <input type="<?= $value['input_type'] ?>" name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= isset($value['placeholder']) ? $value['placeholder'] : null ?>" value="<?= (str_contains('password', $value['variable'])) ? null : $element['row_data']->{$variable} ?>" <?= isset($value['required']) && $value['required'] == true ? 'required' : null ?> <?= $attribute ?>>
                        <?php elseif ($value['type'] == 'textarea') : ?>
                            <?php $variable = $value['variable']; ?>

                            <?php if (str_contains($variable, 'validated')) : ?>
                                <?php $variable = explode('_', $variable)[1]; ?>
                            <?php elseif (str_contains($variable, 'required')) : ?>
                                <?php
                                $variable = explode('_', $variable);
                                array_shift($variable);

                                $variable = implode('_', $variable);
                                ?>
                            <?php endif; ?>

                            <textarea name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= $value['placeholder'] ?>" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>><?= $element['row_data']->{$variable} ?></textarea>
                        <?php elseif ($value['type'] == 'select') : ?>
                            <select name="<?= $value['variable'] ?>" class="form-control" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>>
                                <?php if (isset($value['defaultvalue']['data'])) : ?>
                                    <?php foreach ($value['defaultvalue']['data'] as $k => $v) : ?>
                                        <?php if (!isset($value['defaultvalue']['type']) || $value['defaultvalue']['type'] == 'object') : ?>
                                            <?php if (!is_array($value['defaultvalue']['selected'])) : ?>
                                                <option value="<?= $v->{$value['defaultvalue']['value']} ?>" <?= $v->{$value['defaultvalue']['value']} == $value['defaultvalue']['selected'] ? 'selected' : null ?>><?= $v->{$value['defaultvalue']['text']} ?></option>
                                            <?php else : ?>
                                                <option value="<?= $v->{$value['defaultvalue']['value']} ?>" <?= in_array($v->{$value['defaultvalue']['value']}, $value['defaultvalue']['selected']) ? 'selected' : null ?>><?= $v->{$value['defaultvalue']['text']} ?></option>
                                            <?php endif; ?>
                                        <?php elseif ($value['defaultvalue']['type'] == 'array') : ?>
                                            <option value="<?= $v[$value['defaultvalue']['value']] ?>" <?= $v[$value['defaultvalue']['value']] == $value['defaultvalue']['selected'] ? 'selected' : null ?>><?= $v[$value['defaultvalue']['text']] ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        <?php endif; ?>

                        <?php if (isset($value['helpertext']) && !empty($value['helpertext'])) : ?>
                            <small><?= $value['helpertext'] ?></small>
                        <?php endif; ?>
                    </div>
                <?php else : ?>
                    <div class="row">
                        <?php foreach ($value['data'] as $key => $value) : ?>
                            <?php
                            $attr = isset($value['attr']) ? $value['attr'] : array();

                            $attribute = "";
                            foreach ($attr as $k => $v) {
                                $attribute .= "$k='$v' ";
                            }
                            ?>

                            <div class="<?= $value['parent_class'] ?>">
                                <div class="<?= isset($value['input_type']) ? ($value['input_type'] == 'hidden' ? 'd-none' : 'mb-3') : 'mb-3' ?>">
                                    <?php if (isset($value['label'])) : ?>
                                        <label for="" class="form-label"><?= $value['label'] ?> <?= isset($value['required']) && $value['required'] ? '<span class="text-danger">*</span>' : null ?></label>
                                    <?php endif; ?>

                                    <?php if ($value['type'] == 'input') : ?>
                                        <?php $variable = $value['variable']; ?>

                                        <?php if (str_contains($variable, 'validated')) : ?>
                                            <?php if (str_contains($variable, 'required')) : ?>
                                                <?php $variable = explode('_', $variable)[2]; ?>
                                            <?php else : ?>
                                                <?php $variable = explode('_', $variable)[1]; ?>
                                            <?php endif; ?>
                                        <?php elseif (str_contains($variable, 'required')) : ?>
                                            <?php
                                            $variable = explode('_', $variable);
                                            array_shift($variable);

                                            $variable = implode('_', $variable);
                                            ?>
                                        <?php endif; ?>

                                        <input type="<?= $value['input_type'] ?>" name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= isset($value['placeholder']) ? $value['placeholder'] : null ?>" value="<?= (str_contains('password', $value['variable'])) ? null : $element['row_data']->{$variable} ?>" <?= isset($value['required']) && $value['required'] ? 'required' : null ?> <?= $attribute ?>>
                                    <?php elseif ($value['type'] == 'textarea') : ?>
                                        <?php $variable = $value['variable']; ?>

                                        <?php if (str_contains($variable, 'validated')) : ?>
                                            <?php $variable = explode('_', $variable)[1]; ?>
                                        <?php elseif (str_contains($variable, 'required')) : ?>
                                            <?php
                                            $variable = explode('_', $variable);
                                            array_shift($variable);

                                            $variable = implode('_', $variable);
                                            ?>
                                        <?php endif; ?>

                                        <textarea name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= $value['placeholder'] ?>" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>><?= $element['row_data']->{$variable} ?></textarea>
                                    <?php elseif ($value['type'] == 'select') : ?>
                                        <select name="<?= $value['variable'] ?>" class="form-control" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>>
                                            <?php if (isset($value['defaultvalue']['data'])) : ?>
                                                <?php foreach ($value['defaultvalue']['data'] as $k => $v) : ?>
                                                    <?php if (!isset($value['defaultvalue']['type']) || $value['defaultvalue']['type'] == 'object') : ?>
                                                        <?php if (!is_array($value['defaultvalue']['selected'])) : ?>
                                                            <option value="<?= $v->{$value['defaultvalue']['value']} ?>" <?= $v->{$value['defaultvalue']['value']} == $value['defaultvalue']['selected'] ? 'selected' : null ?>><?= $v->{$value['defaultvalue']['text']} ?></option>
                                                        <?php else : ?>
                                                            <option value="<?= $v->{$value['defaultvalue']['value']} ?>" <?= in_array($v->{$value['defaultvalue']['value']}, $value['defaultvalue']['selected']) ? 'selected' : null ?>><?= $v->{$value['defaultvalue']['text']} ?></option>
                                                        <?php endif; ?>
                                                    <?php elseif ($value['defaultvalue']['type'] == 'array') : ?>
                                                        <option value="<?= $v[$value['defaultvalue']['value']] ?>" <?= $v[$value['defaultvalue']['value']] == $value['defaultvalue']['selected'] ? 'selected' : null ?>><?= $v[$value['defaultvalue']['text']] ?></option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    <?php endif; ?>

                                    <?php if (isset($value['helpertext'])) : ?>
                                        <small><?= $value['helpertext'] ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>

            <div class="text-end mt-3">
                <?php foreach ($element['footer_button'] as $key => $value) : ?>
                    <?php $attribute = ""; ?>

                    <?php if (isset($value['attr'])) : ?>
                        <?php foreach ($value['attr'] as $k => $v) : ?>
                            <?php $attribute .= "$k='$v'" ?>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if ($value['type'] == 'a') : ?>
                        <a href="<?= $value['href'] ?>" class="<?= $value['class'] ?>"><?= $value['text'] ?></a>
                    <?php elseif ($value['type'] == 'button') : ?>
                        <button type="submit" class="<?= $value['class'] ?>" <?= $attribute ?>><?= $value['text'] ?></button>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </form>
    </div>
</div>