<!-- Breadcrumb -->
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Laporan BUMDes</h6>
    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url('dashboard') ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Laporan BUMDes</li>
    </ul>
</div>

<!-- Main Card -->
<div class="card basic-data-table">
    <div class="card-header d-flex flex-wrap align-items-center justify-content-between">
        <h5 class="card-title mb-0">Laporan BUMDes</h5>
        <?php if (!isSuperAdmin()): ?>
            <a href="<?= base_url('bumdes_report/add') ?>" class="btn btn-primary radius-8 px-20 py-11 d-flex gap-1 align-items-center">
                <iconify-icon icon="ic:baseline-add" class="icon text-xl line-height-1"></iconify-icon>
                Tambah Laporan
            </a>
        <?php endif; ?>
    </div>
    <div class="card-body">
        <!-- Statistics Cards -->
        <div class="row row-cols-xl-2 row-cols-lg-2 row-cols-sm-2 row-cols-1 gy-4 mb-24">
            <div class="col">
                <div class="card shadow-none border bg-gradient-start-1 h-100">
                    <div class="card-body p-20">
                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                            <div>
                                <p class="fw-medium text-primary-light mb-1">Total Laporan</p>
                                <h6 class="mb-0"><?= isset($statistics['total_reports']) ? number_format($statistics['total_reports']) : 0 ?></h6>
                            </div>
                            <div class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                                <iconify-icon icon="solar:document-text-bold" class="text-white text-2xl mb-0"></iconify-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card shadow-none border bg-gradient-start-4 h-100">
                    <div class="card-body p-20">
                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                            <div>
                                <p class="fw-medium text-primary-light mb-1">Total Laba</p>
                                <h6 class="mb-0">Rp <?= isset($statistics['total_laba']) ? number_format($statistics['total_laba'], 0, ',', '.') : '0' ?></h6>
                            </div>
                            <div class="w-50-px h-50-px bg-purple rounded-circle d-flex justify-content-center align-items-center">
                                <iconify-icon icon="solar:wallet-money-bold" class="text-white text-2xl mb-0"></iconify-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="row">
            <div class="col-md-3">
                <select class="form-control" id="filter-year">
                    <option value="">Pilih Tahun</option>
                    <?php foreach ($years as $y): ?>
                        <option value="<?= $y ?>" <?= $year == $y ? 'selected' : '' ?>><?= $y ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <?php if (isSuperAdmin() && !empty($bumdes_list)): ?>
                <div class="col-md-3">
                    <select class="form-control" id="filter-bumdes">
                        <option value="">Semua BUMDes</option>
                        <?php foreach ($bumdes_list as $bumdes): ?>
                            <option value="<?= $bumdes->id ?>" <?= $bumdes_id == $bumdes->id ? 'selected' : '' ?>>
                                <?= $bumdes->businessname ?> (<?= $bumdes->name ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>

            <div class="col-md-3">
                <button type="button" class="btn btn-primary" id="btn-filter">Filter</button>
                <button type="button" class="btn btn-secondary" id="btn-reset">Reset</button>
            </div>
        </div>

        <!-- Table -->
        <div class="table-responsive mt-3">
            <table class="table bordered-table mb-0 datatables" id="dataTable" data-page-length='10'>
                <thead>
                    <tr>
                        <th scope="col">No</th>
                        <?php if (isSuperAdmin()): ?>
                            <th scope="col">BUMDes</th>
                        <?php endif; ?>
                        <th scope="col">Kode Laporan</th>
                        <th scope="col">Periode</th>
                        <th scope="col">Modal</th>
                        <th scope="col">Omset</th>
                        <th scope="col">Laba</th>
                        <th scope="col">Balance</th>
                        <th scope="col">Aksi</th>
                    </tr>
                </thead>
                <tbody>

                    <?php $no = 1;
                    foreach ($reports as $report): ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <?php if (isSuperAdmin()): ?>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="w-32-px h-32-px bg-primary-100 text-primary-600 rounded-circle d-flex justify-content-center align-items-center flex-shrink-0 me-12">
                                            <iconify-icon icon="solar:buildings-2-bold" class="text-sm"></iconify-icon>
                                        </div>
                                        <div>
                                            <h6 class="text-sm mb-0 fw-medium"><?= $report->bumdes_name ?></h6>
                                        </div>
                                    </div>
                                </td>
                            <?php endif; ?>
                            <td><?= $report->report_code ?></td>
                            <td><?= date('M Y', strtotime($report->report_period)) ?></td>
                            <td>Rp <?= number_format($report->modal_amount, 0, ',', '.') ?></td>
                            <td>Rp <?= number_format($report->omset_amount, 0, ',', '.') ?></td>
                            <td>Rp <?= number_format($report->laba_amount, 0, ',', '.') ?></td>
                            <td>Rp <?= number_format($report->balance_check, 0, ',', '.') ?></td>

                            <td>
                                <a href="javascript:void(0)" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center view-btn" data-id="<?= $report->id ?>">
                                    <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                                </a>
                                <?php if (!isSuperAdmin()): ?>
                                    <a href="<?= base_url('bumdes_report/edit/' . $report->id) ?>" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                        <iconify-icon icon="lucide:edit"></iconify-icon>
                                    </a>
                                    <a href="javascript:void(0)" class="w-32-px h-32-px bg-danger-focus text-danger-main rounded-circle d-inline-flex align-items-center justify-content-center delete-btn" data-id="<?= $report->id ?>">
                                        <iconify-icon icon="mingcute:delete-2-line"></iconify-icon>
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- View Modal -->
<div class="modal fade" id="viewModal" tabindex="-1" aria-labelledby="viewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewModalLabel">Detail Laporan BUMDes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="viewModalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        // Temporarily disable DataTables to avoid column errors
        console.log('Table loaded - DataTables disabled temporarily');

        // View button
        $('.view-btn').click(function() {
            var id = $(this).data('id');
            $.get('<?= base_url('bumdes_report/view/') ?>' + id, function(data) {
                $('#viewModalBody').html(data);
                $('#viewModal').modal('show');
            });
        });

        // Delete button with SweetAlert
        $('.delete-btn').click(function() {
            var id = $(this).data('id');
            swal({
                title: 'Apakah Anda yakin?',
                text: 'Laporan BUMDes ini akan dihapus secara permanen dari database dan tidak dapat dikembalikan!',
                icon: 'warning',
                buttons: {
                    cancel: {
                        text: 'Batal',
                        value: null,
                        visible: true,
                        className: 'btn btn-secondary',
                        closeModal: true,
                    },
                    confirm: {
                        text: 'Ya, Hapus!',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                },
                dangerMode: true
            }).then(function(isConfirm) {
                if (isConfirm) {
                    // Show loading
                    swal({
                        title: 'Menghapus...',
                        text: 'Sedang menghapus laporan BUMDes',
                        icon: 'info',
                        buttons: false,
                        closeOnClickOutside: false,
                        closeOnEsc: false
                    });

                    // Perform delete
                    $.ajax({
                        url: '<?= base_url('bumdes_report/delete/') ?>' + id,
                        type: 'POST',
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.RESULT === 'OK') {
                                swal({
                                    title: 'Berhasil!',
                                    text: response.MESSAGE || 'Laporan BUMDes berhasil dihapus',
                                    icon: 'success',
                                    button: 'OK'
                                }).then(function() {
                                    window.location.reload();
                                });
                            } else {
                                swal({
                                    title: 'Gagal!',
                                    text: response.MESSAGE || 'Terjadi kesalahan saat menghapus laporan',
                                    icon: 'error',
                                    button: 'OK'
                                });
                            }
                        },
                        error: function() {
                            swal({
                                title: 'Error!',
                                text: 'Terjadi kesalahan sistem. Silakan coba lagi.',
                                icon: 'error',
                                button: 'OK'
                            });
                        }
                    });
                }
            });
        });

        // Filter functionality
        $('#btn-filter').click(function() {
            var year = $('#filter-year').val();
            var bumdes_id = $('#filter-bumdes').val();
            var url = '<?= base_url('bumdes_report') ?>?';
            var params = [];

            if (year) params.push('year=' + year);
            if (bumdes_id) params.push('bumdes_id=' + bumdes_id);

            if (params.length > 0) {
                url += params.join('&');
            }

            window.location.href = url;
        });

        $('#btn-reset').click(function() {
            window.location.href = '<?= base_url('bumdes_report') ?>';
        });
    };
</script>