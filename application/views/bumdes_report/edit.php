<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Edit <PERSON><PERSON>an BUMDes</h6>
    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url('dashboard') ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">
            <a href="<?= base_url('bumdes_report') ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                Laporan BUMDes
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Edit <PERSON></li>
    </ul>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <h5 class="card-title mb-0">Edit Laporan BUMDes</h5>
                    <a href="<?= base_url('bumdes_report') ?>" class="btn btn-neutral-500 radius-8 px-20 py-11 d-flex gap-1 align-items-center">
                        <iconify-icon icon="ic:baseline-arrow-back" class="icon text-xl line-height-1"></iconify-icon>
                        Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form id="form-edit-report">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label for="report_period" class="form-label fw-semibold text-primary-light text-sm mb-8">Periode Laporan</label>
                                <input type="number" class="form-control radius-8" id="report_period" name="report_period"
                                    value="<?= date('Y', strtotime($data->report_period)) ?>" readonly>
                                <div class="text-gray-600 text-sm mt-4">Periode tidak dapat diubah</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label for="report_code" class="form-label fw-semibold text-primary-light text-sm mb-8">Kode Laporan</label>
                                <input type="text" class="form-control radius-8" value="<?= $data->report_code ?>" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label for="modal_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Tipe Modal <span class="text-danger-600">*</span></label>
                                <select class="form-control radius-8" id="modal_type" name="modal_type" required>
                                    <option value="">Pilih Tipe Modal</option>
                                    <option value="saldo_awal" <?= $data->modal_type === 'saldo_awal' ? 'selected' : '' ?>>Dari Saldo Awal</option>
                                    <option value="manual" <?= $data->modal_type === 'manual' ? 'selected' : '' ?>>Input Manual</option>
                                </select>
                                <div class="text-gray-600 text-sm mt-4">Pilih sumber modal untuk laporan</div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="modal_manual_section" style="display: <?= $data->modal_type === 'manual' ? 'block' : 'none' ?>;">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label for="modal_manual" class="form-label fw-semibold text-primary-light text-sm mb-8">Modal Manual <span class="text-danger-600">*</span></label>
                                <input type="number" class="form-control radius-8" id="modal_manual" name="modal_manual"
                                    min="0" step="0.01" value="<?= $data->modal_manual ?>" placeholder="Masukkan jumlah modal">
                                <div class="text-gray-600 text-sm mt-4">Masukkan jumlah modal dalam rupiah</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label for="laba_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Tipe Laba <span class="text-danger-600">*</span></label>
                                <select class="form-control radius-8" id="laba_type" name="laba_type" required>
                                    <option value="">Pilih Tipe Laba</option>
                                    <option value="calculated" <?= $data->laba_type === 'calculated' ? 'selected' : '' ?>>Hitung Otomatis (Pendapatan - Pengeluaran)</option>
                                    <option value="manual" <?= $data->laba_type === 'manual' ? 'selected' : '' ?>>Input Manual</option>
                                </select>
                                <div class="text-gray-600 text-sm mt-4">Pilih cara menghitung laba</div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="laba_manual_section" style="display: <?= $data->laba_type === 'manual' ? 'block' : 'none' ?>;">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label for="laba_manual" class="form-label fw-semibold text-primary-light text-sm mb-8">Laba Manual <span class="text-danger-600">*</span></label>
                                <input type="number" class="form-control radius-8" id="laba_manual" name="laba_manual"
                                    step="0.01" value="<?= $data->laba_manual ?>" placeholder="Masukkan jumlah laba">
                                <div class="text-gray-600 text-sm mt-4">Masukkan jumlah laba dalam rupiah (bisa negatif)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-20">
                                <label for="notes" class="form-label fw-semibold text-primary-light text-sm mb-8">Catatan</label>
                                <textarea class="form-control radius-8" id="notes" name="notes" rows="3"
                                    placeholder="Tambahkan catatan untuk laporan ini (opsional)"><?= $data->notes ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Current Values -->
                    <div class="mt-24">
                        <div class="d-flex align-items-center justify-content-between gap-3 mb-20">
                            <h6 class="fw-semibold mb-0">Nilai Saat Ini</h6>
                            <div class="d-flex align-items-center gap-2">
                                <iconify-icon icon="solar:database-bold" class="text-success-600 text-xl"></iconify-icon>
                                <span class="text-sm text-secondary-light">Data tersimpan</span>
                            </div>
                        </div>

                        <div class="row row-cols-xl-5 row-cols-lg-3 row-cols-sm-2 row-cols-1 gy-4">
                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-1 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-cyan rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:wallet-money-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Modal</p>
                                        <h5 class="mb-0"><?= 'Rp ' . number_format($data->modal_amount, 0, ',', '.') ?></h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-2 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-success-main rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:chart-square-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Omset</p>
                                        <h5 class="mb-0"><?= 'Rp ' . number_format($data->omset_amount, 0, ',', '.') ?></h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-3 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-purple rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:medal-star-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Laba</p>
                                        <h5 class="mb-0"><?= 'Rp ' . number_format($data->laba_amount, 0, ',', '.') ?></h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-4 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-warning-main rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:card-send-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Pengeluaran</p>
                                        <h5 class="mb-0"><?= 'Rp ' . number_format($data->total_pengeluaran, 0, ',', '.') ?></h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-5 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-info rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:calculator-minimalistic-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Balance Check</p>
                                        <h5 class="mb-0"><?= 'Rp ' . number_format($data->balance_check, 0, ',', '.') ?></h5>
                                        <p class="fw-medium text-xs text-secondary-light mb-0 mt-8">Modal + Laba</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="mt-24" id="preview_section" style="display: none;">
                        <div class="d-flex align-items-center justify-content-between gap-3 mb-20">
                            <h6 class="fw-semibold mb-0">Preview Perhitungan Baru</h6>
                            <div class="d-flex align-items-center gap-2">
                                <iconify-icon icon="solar:calculator-bold" class="text-primary-600 text-xl"></iconify-icon>
                                <span class="text-sm text-secondary-light">Hasil kalkulasi terbaru</span>
                            </div>
                        </div>

                        <div class="row row-cols-xl-5 row-cols-lg-3 row-cols-sm-2 row-cols-1 gy-4">
                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-1 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-cyan rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:wallet-money-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Modal</p>
                                        <h5 class="mb-0" id="preview_modal">Rp 0</h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-2 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-success-main rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:chart-square-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Omset</p>
                                        <h5 class="mb-0" id="preview_omset">Rp 0</h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-3 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-purple rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:medal-star-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Laba</p>
                                        <h5 class="mb-0" id="preview_laba">Rp 0</h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-4 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-warning-main rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:card-send-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Pengeluaran</p>
                                        <h5 class="mb-0" id="preview_pengeluaran">Rp 0</h5>
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="card shadow-none border bg-gradient-start-5 h-100">
                                    <div class="card-body p-20 text-center">
                                        <div class="w-60-px h-60-px bg-info rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                                            <iconify-icon icon="solar:calculator-minimalistic-bold" class="text-white text-3xl mb-0"></iconify-icon>
                                        </div>
                                        <p class="fw-medium text-primary-light mb-8">Balance Check</p>
                                        <h5 class="mb-0" id="preview_balance">Rp 0</h5>
                                        <p class="fw-medium text-xs text-secondary-light mb-0 mt-8">Modal + Laba</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center gap-2 mt-24">
                        <button type="button" class="btn btn-info-600 radius-8 px-20 py-11 d-flex gap-1 align-items-center" id="btn-preview">
                            <iconify-icon icon="solar:calculator-bold" class="icon text-xl line-height-1"></iconify-icon>
                            Preview Perhitungan
                        </button>
                        <button type="submit" class="btn btn-primary-600 radius-8 px-20 py-11 d-flex gap-1 align-items-center">
                            <iconify-icon icon="solar:diskette-bold" class="icon text-xl line-height-1"></iconify-icon>
                            Update Laporan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        // Toggle modal manual section
        $('#modal_type').on('change', function() {
            console.log('Modal type changed to:', $(this).val()); // Debug log
            if ($(this).val() === 'manual') {
                $('#modal_manual_section').show();
                $('#modal_manual').prop('required', true);
            } else {
                $('#modal_manual_section').hide();
                $('#modal_manual').prop('required', false);
            }
        });

        // Toggle laba manual section
        $('#laba_type').on('change', function() {
            console.log('Laba type changed to:', $(this).val()); // Debug log
            if ($(this).val() === 'manual') {
                $('#laba_manual_section').show();
                $('#laba_manual').prop('required', true);
            } else {
                $('#laba_manual_section').hide();
                $('#laba_manual').prop('required', false);
            }
        });

        // Preview calculation
        $('#btn-preview').click(function() {
            var period = $('#report_period').val();
            var modalType = $('#modal_type').val();
            var modalManual = $('#modal_manual').val();
            var labaType = $('#laba_type').val();
            var labaManual = $('#laba_manual').val();

            if (!period || !modalType || !labaType) {
                swal({
                    title: 'Peringatan!',
                    text: 'Harap lengkapi tipe modal dan tipe laba terlebih dahulu',
                    icon: 'warning',
                    button: 'OK'
                });
                return;
            }

            if (modalType === 'manual' && !modalManual) {
                swal({
                    title: 'Peringatan!',
                    text: 'Harap masukkan modal manual',
                    icon: 'warning',
                    button: 'OK'
                });
                return;
            }

            if (labaType === 'manual' && !labaManual) {
                swal({
                    title: 'Peringatan!',
                    text: 'Harap masukkan laba manual',
                    icon: 'warning',
                    button: 'OK'
                });
                return;
            }

            $.ajax({
                url: '<?= base_url('bumdes_report/calculate_preview') ?>',
                type: 'POST',
                data: {
                    period: period,
                    modal_type: modalType,
                    modal_manual: modalManual,
                    laba_type: labaType,
                    laba_manual: labaManual
                },
                dataType: 'json',
                beforeSend: function() {
                    $('#btn-preview').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghitung...');
                    // Bersihkan informasi HPP sebelumnya
                    $('#preview_section .alert-info').remove();
                },
                success: function(response) {
                    console.log('Preview response:', response); // Debug log
                    if (response && response.RESULT === 'OK') {
                        var data = response.DATA;
                        if (data) {
                            $('#preview_modal').text(formatRupiah(data.modal_amount || 0));
                            $('#preview_omset').text(formatRupiah(data.omset_amount || 0));
                            $('#preview_laba').text(formatRupiah(data.laba_amount || 0));
                            $('#preview_pengeluaran').text(formatRupiah(data.total_pengeluaran || 0));
                            $('#preview_balance').text(formatRupiah(data.balance_check || 0));

                            // Tampilkan informasi HPP jika ada
                            if (data.has_hpp && data.total_hpp > 0) {
                                var hppInfo = '<div class="alert alert-info bg-info-100 text-info-600 border-info-100 px-24 py-11 mb-0 fw-semibold text-lg radius-8 mt-3" role="alert">' +
                                    '<div class="d-flex align-items-start justify-content-between text-lg">' +
                                    '<div class="d-flex align-items-start gap-2">' +
                                    '<iconify-icon icon="mynaui:check-octagon" class="icon text-xl mt-4 flex-shrink-0"></iconify-icon>' +
                                    '<div>' +
                                    '<div class="fw-semibold mb-2">Informasi HPP Terdeteksi</div>' +
                                    '<div class="fw-medium text-info-600 text-sm">' +
                                    '<div class="mb-2"><strong>Total HPP:</strong> ' + formatRupiah(data.total_hpp) + '</div>' +
                                    '<div class="mb-2"><strong>Keuntungan dari transaksi ber-HPP:</strong> ' + formatRupiah(data.keuntungan_with_hpp) + '</div>' +
                                    '<div class="mb-2"><strong>Omset tanpa HPP:</strong> ' + formatRupiah(data.omset_tanpa_hpp) + '</div>' +
                                    '<div class="mt-3 p-2 bg-info-50 rounded border-start border-info-600 border-start-width-3-px">' +
                                    '<strong>Rumus Laba:</strong> Keuntungan HPP + (Omset tanpa HPP - Pengeluaran)' +
                                    '</div>' +
                                    '</div>' +
                                    '</div>' +
                                    '</div>' +
                                    '</div>' +
                                    '</div>';
                                $('#preview_section').append(hppInfo);
                            }

                            $('#preview_section').show();
                        } else {
                            swal({
                                title: 'Error!',
                                text: 'Data preview tidak tersedia',
                                icon: 'error',
                                button: 'OK'
                            });
                        }
                    } else {
                        var errorMessage = 'Terjadi kesalahan saat menghitung preview';
                        if (response && response.MESSAGE) {
                            errorMessage = response.MESSAGE;
                        }
                        swal({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            button: 'OK'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', xhr, status, error); // Debug log
                    swal({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menghitung preview. Silakan coba lagi.',
                        icon: 'error',
                        button: 'OK'
                    });
                },
                complete: function() {
                    $('#btn-preview').prop('disabled', false).html('<i class="fas fa-calculator"></i> Preview Perhitungan');
                }
            });
        });

        // Submit form
        $('#form-edit-report').submit(function(e) {
            e.preventDefault();

            $.ajax({
                url: '<?= base_url('bumdes_report/update/' . $data->id) ?>',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                beforeSend: function() {
                    $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');
                },
                success: function(response) {
                    console.log('Submit response:', response); // Debug log
                    if (response && (response.RESULT === 'OK' || response.status === 'OK')) {
                        swal({
                            title: 'Berhasil!',
                            text: response.MESSAGE || response.message || 'Laporan berhasil diupdate',
                            icon: 'success',
                            button: 'OK'
                        }).then(() => {
                            window.location.href = '<?= base_url('bumdes_report') ?>';
                        });
                    } else {
                        var errorMessage = 'Terjadi kesalahan saat mengupdate laporan';
                        if (response && (response.MESSAGE || response.message)) {
                            errorMessage = response.MESSAGE || response.message;
                        }
                        swal({
                            title: 'Gagal!',
                            text: errorMessage,
                            icon: 'error',
                            button: 'OK'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Submit Error:', xhr, status, error); // Debug log
                    swal({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengupdate data. Silakan coba lagi.',
                        icon: 'error',
                        button: 'OK'
                    });
                },
                complete: function() {
                    $('button[type="submit"]').prop('disabled', false).html('<iconify-icon icon="solar:diskette-bold" class="icon text-xl line-height-1"></iconify-icon> Update Laporan');
                }
            });
        });

        // Format rupiah function
        function formatRupiah(amount) {
            return 'Rp ' + parseFloat(amount).toLocaleString('id-ID', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            });
        }
    };
</script>