<!-- Header Section -->
<div class="border-bottom border-neutral-200 pb-16 mb-20">
    <div>
        <h6 class="fw-semibold mb-4 text-md"><?= $data->report_code ?></h6>
        <p class="text-sm text-secondary-light mb-0">Laporan BUMDes - <?= date('F Y', strtotime($data->report_period)) ?></p>
    </div>
</div>

<!-- Financial Details Section -->
<div class="mb-24">
    <h6 class="fw-semibold mb-16 text-md">Detail Keuangan</h6>

    <div class="row gy-3">
        <div class="col-md-6">
            <div class="d-flex align-items-center justify-content-between p-16 bg-primary-50 border border-primary-200 rounded-8">
                <div class="d-flex align-items-center gap-12">
                    <div class="w-40-px h-40-px bg-primary-600 rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:wallet-money-bold" class="text-white text-lg"></iconify-icon>
                    </div>
                    <div>
                        <p class="text-sm text-secondary-light mb-0">Modal</p>
                        <p class="fw-semibold text-primary-600 mb-0"><?= 'Rp ' . number_format($data->modal_amount ?? 0, 0, ',', '.') ?></p>
                    </div>
                </div>
                <span class="text-xs text-secondary-light"><?= $data->modal_type === 'manual' ? 'Manual' : 'Saldo Awal' ?></span>
            </div>
        </div>

        <div class="col-md-6">
            <div class="d-flex align-items-center justify-content-between p-16 bg-success-50 border border-success-200 rounded-8">
                <div class="d-flex align-items-center gap-12">
                    <div class="w-40-px h-40-px bg-success-600 rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:chart-square-bold" class="text-white text-lg"></iconify-icon>
                    </div>
                    <div>
                        <p class="text-sm text-secondary-light mb-0">Omset</p>
                        <p class="fw-semibold text-success-600 mb-0"><?= 'Rp ' . number_format($data->omset_amount ?? 0, 0, ',', '.') ?></p>
                    </div>
                </div>
                <span class="text-xs text-secondary-light">Pendapatan</span>
            </div>
        </div>

        <div class="col-md-6">
            <div class="d-flex align-items-center justify-content-between p-16 bg-info-50 border border-info-200 rounded-8">
                <div class="d-flex align-items-center gap-12">
                    <div class="w-40-px h-40-px bg-info rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:medal-star-bold" class="text-white text-lg"></iconify-icon>
                    </div>
                    <div>
                        <p class="text-sm text-secondary-light mb-0">Laba</p>
                        <p class="fw-semibold text-info mb-0"><?= 'Rp ' . number_format($data->laba_amount ?? 0, 0, ',', '.') ?></p>
                    </div>
                </div>
                <span class="text-xs text-secondary-light"><?= $data->laba_type === 'manual' ? 'Manual' : 'Otomatis' ?></span>
            </div>
        </div>

        <div class="col-md-6">
            <div class="d-flex align-items-center justify-content-between p-16 bg-warning-50 border border-warning-200 rounded-8">
                <div class="d-flex align-items-center gap-12">
                    <div class="w-40-px h-40-px bg-warning-600 rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:card-send-bold" class="text-white text-lg"></iconify-icon>
                    </div>
                    <div>
                        <p class="text-sm text-secondary-light mb-0">Pengeluaran</p>
                        <p class="fw-semibold text-warning-600 mb-0"><?= 'Rp ' . number_format($data->total_pengeluaran ?? 0, 0, ',', '.') ?></p>
                    </div>
                </div>
                <span class="text-xs text-secondary-light">Total Biaya</span>
            </div>
        </div>
    </div>

    <!-- Balance Check - Highlighted -->
    <div class="mt-16">
        <div class="p-20 bg-success-50 border border-success-200 rounded-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-16">
                    <div class="w-48-px h-48-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:calculator-minimalistic-bold" class="text-white text-xl"></iconify-icon>
                    </div>
                    <div>
                        <p class="text-sm text-secondary-light mb-4">Balance Check</p>
                        <h5 class="fw-bold text-success-main mb-0"><?= 'Rp ' . number_format($data->balance_check ?? 0, 0, ',', '.') ?></h5>
                        <p class="text-xs text-secondary-light mb-0">Modal + Laba</p>
                    </div>
                </div>
                <div class="text-end">
                    <iconify-icon icon="solar:verified-check-bold" class="text-success-main text-2xl"></iconify-icon>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($data->notes)): ?>
    <!-- Notes Section -->
    <div class="mb-24">
        <div class="p-16 bg-warning-50 border border-warning-200 rounded-8">
            <div class="d-flex align-items-start gap-12">
                <iconify-icon icon="solar:notes-bold" class="text-warning-600 text-lg mt-2"></iconify-icon>
                <div class="flex-grow-1">
                    <h6 class="fw-semibold mb-8 text-sm">Catatan</h6>
                    <p class="text-secondary-light mb-0 text-sm"><?= nl2br(htmlspecialchars($data->notes)) ?></p>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Footer Info -->
<div class="border-top border-neutral-200 pt-16">
    <div class="row">
        <div class="col-6">
            <div class="d-flex align-items-center gap-8">
                <iconify-icon icon="solar:calendar-bold" class="text-secondary-light text-lg"></iconify-icon>
                <div>
                    <p class="text-xs text-secondary-light mb-0">Dibuat</p>
                    <p class="fw-medium text-sm mb-0"><?= date('d M Y, H:i', strtotime($data->createddate)) ?></p>
                </div>
            </div>
        </div>
        <?php if (!empty($data->updateddate)): ?>
            <div class="col-6">
                <div class="d-flex align-items-center gap-8">
                    <iconify-icon icon="solar:pen-bold" class="text-secondary-light text-lg"></iconify-icon>
                    <div>
                        <p class="text-xs text-secondary-light mb-0">Diperbarui</p>
                        <p class="fw-medium text-sm mb-0"><?= date('d M Y, H:i', strtotime($data->updateddate)) ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>