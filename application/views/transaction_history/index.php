<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">History Perubahan Status Transaksi</h6>
    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">History Status Transaksi</li>
    </ul>
</div>

<!-- Filter Section -->
<div class="card mb-24">
    <div class="card-header">
        <h6 class="card-title mb-0">Filter Data</h6>
    </div>
    <div class="card-body">
        <form method="POST" action="<?= base_url('transaction_history') ?>">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Tanggal Dari</label>
                        <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Tanggal Sampai</label>
                        <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="">Semua Status</option>
                            <option value="Lunas" <?= $status === 'Lunas' ? 'selected' : '' ?>>Lunas</option>
                            <option value="Menunggu Pembayaran" <?= $status === 'Menunggu Pembayaran' ? 'selected' : '' ?>>Menunggu Pembayaran</option>
                            <option value="Dibatalkan" <?= $status === 'Dibatalkan' ? 'selected' : '' ?>>Dibatalkan</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Jenis Transaksi</label>
                        <select name="transaction_type" class="form-control">
                            <option value="">Semua Jenis</option>
                            <option value="Pendapatan" <?= $transaction_type === 'Pendapatan' ? 'selected' : '' ?>>Pendapatan</option>
                            <option value="Pengeluaran" <?= $transaction_type === 'Pengeluaran' ? 'selected' : '' ?>>Pengeluaran</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary d-flex gap-1 align-items-center">
                    <iconify-icon icon="solar:filter-bold" class="icon text-lg"></iconify-icon>
                    Filter
                </button>
                <button type="button" class="btn btn-success d-flex gap-1 align-items-center" onclick="exportData()">
                    <iconify-icon icon="solar:export-bold" class="icon text-lg"></iconify-icon>
                    Export CSV
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<?php if (!empty($statistics)): ?>
    <div class="row gy-4 mb-24">
        <?php foreach ($statistics as $stat): ?>
            <div class="col-md-4">
                <div class="card shadow-none border h-100">
                    <div class="card-body p-20">
                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                            <div>
                                <p class="fw-medium text-primary-light mb-1">Status: <?= $stat->new_status ?></p>
                                <h6 class="mb-0"><?= number_format($stat->count) ?> Perubahan</h6>
                            </div>
                            <div class="w-50-px h-50-px bg-primary rounded-circle d-flex justify-content-center align-items-center">
                                <iconify-icon icon="solar:chart-2-bold" class="text-white text-2xl mb-0"></iconify-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- History Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">Data History Perubahan Status</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table bordered-table mb-0 datatables">
                <thead>
                    <tr>
                        <th>Kode Transaksi</th>
                        <th>Tanggal Transaksi</th>
                        <th>Nominal</th>
                        <th>Status Lama</th>
                        <th>Status Baru</th>
                        <th>Diubah Oleh</th>
                        <th>Tanggal Perubahan</th>
                        <?php if (isSuperAdmin() || isBumdes()): ?>
                            <th>Unit Usaha</th>
                        <?php endif; ?>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($history as $item): ?>
                        <tr>
                            <td>
                                <span class="fw-medium text-primary-600"><?= $item->transactioncode ?></span>
                            </td>
                            <td><?= tgl_indo($item->transactiondate) ?></td>
                            <td>Rp <?= number_format($item->amount ?? 0, 0, ',', '.') ?></td>
                            <td>
                                <?php if ($item->old_status): ?>
                                    <span class="bg-secondary-focus text-secondary-main px-16 py-4 rounded-pill fw-medium text-sm">
                                        <?= $item->old_status ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-secondary-light">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($item->new_status == 'Lunas'): ?>
                                    <span class="bg-success-focus text-success-main px-16 py-4 rounded-pill fw-medium text-sm">Lunas</span>
                                <?php elseif ($item->new_status == 'Menunggu Pembayaran'): ?>
                                    <span class="bg-warning-focus text-warning-main px-16 py-4 rounded-pill fw-medium text-sm">Menunggu Pembayaran</span>
                                <?php elseif ($item->new_status == 'Dibatalkan'): ?>
                                    <span class="bg-danger-focus text-danger-main px-16 py-4 rounded-pill fw-medium text-sm">Dibatalkan</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <span class="fw-medium"><?= $item->changed_by_name ?></span>
                                    <br>
                                    <small class="text-secondary-light"><?= $item->changed_by_role ?></small>
                                </div>
                            </td>
                            <td><?= date('d/m/Y H:i', strtotime($item->changed_date)) ?></td>
                            <?php if (isSuperAdmin() || isBumdes()): ?>
                                <td><?= $item->workunitname ?: '-' ?></td>
                            <?php endif; ?>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary d-flex gap-1 align-items-center" onclick="viewTransactionHistory(<?= $item->transaction_id ?>)">
                                    <iconify-icon icon="solar:eye-bold" class="icon"></iconify-icon>
                                    Detail
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal for Transaction History Detail -->
<div class="modal fade" id="historyDetailModal" tabindex="-1" aria-labelledby="historyDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg-primary-600">
                <h5 class="modal-title text-white" id="historyDetailModalLabel">
                    <i class="ri-history-line me-2"></i>Detail History Perubahan Status
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <!-- Transaction Info Section -->
                <div class="border-bottom p-24">
                    <div class="row" id="transactionInfo">
                        <!-- Transaction info will be loaded here -->
                    </div>
                </div>

                <!-- History Content Section -->
                <div class="p-24">
                    <h6 class="text-md mb-16 fw-semibold">Riwayat Perubahan Status</h6>
                    <div id="historyDetailContent">
                        <!-- History content will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-neutral-50">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="ri-close-line me-1"></i>Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialize DataTable with consistent structure
        $('.datatables').DataTable({
            "pageLength": 10,
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true,
            "responsive": true,
            "order": [
                [6, 'desc']
            ], // Sort by change date (column 6)
            "language": {
                "lengthMenu": "Tampilkan _MENU_ data per halaman",
                "zeroRecords": "Tidak ada data yang ditemukan",
                "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                "infoEmpty": "Tidak ada data tersedia",
                "infoFiltered": "(difilter dari _MAX_ total data)",
                "paginate": {
                    "first": "Pertama",
                    "last": "Terakhir",
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                }
            },
            "columnDefs": [{
                    "orderable": false,
                    "targets": [-1]
                }, // Disable sorting for action column
                {
                    "className": "text-center",
                    "targets": [2, 3, 4, 6]
                } // Center align specific columns
            ]
        });
    });

    function viewTransactionHistory(transactionId) {
        // Show loading state
        $('#historyDetailContent').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        $('#transactionInfo').html('<div class="text-center py-2"><div class="spinner-border spinner-border-sm text-primary" role="status"></div></div>');
        $('#historyDetailModal').modal('show');

        $.ajax({
            url: '<?= base_url('transaction_history/get_transaction_history') ?>',
            type: 'POST',
            data: {
                transaction_id: transactionId
            },
            dataType: 'json',
            success: function(response) {
                if (response.RESULT === 'OK') {
                    // Build transaction info section
                    let transactionInfo = '';
                    if (response.DATA && response.DATA.length > 0) {
                        const firstItem = response.DATA[0];

                        // Format tanggal
                        let formattedDate = '-';
                        if (firstItem.transactiondate) {
                            const date = new Date(firstItem.transactiondate);
                            formattedDate = date.toLocaleDateString('id-ID', {
                                day: '2-digit',
                                month: 'long',
                                year: 'numeric'
                            });
                        }

                        // Format nominal
                        let formattedAmount = '-';
                        if (firstItem.amount) {
                            formattedAmount = 'Rp ' + parseInt(firstItem.amount).toLocaleString('id-ID');
                        }

                        transactionInfo = `
                            <div class="col-md-6">
                                <div class="d-flex align-items-center gap-2 mb-12">
                                    <span class="w-120-px text-secondary-light fw-medium">Kode Transaksi:</span>
                                    <span class="fw-semibold text-primary-600">${firstItem.transactioncode || '-'}</span>
                                </div>
                                <div class="d-flex align-items-center gap-2 mb-12">
                                    <span class="w-120-px text-secondary-light fw-medium">Tanggal:</span>
                                    <span class="fw-medium">${formattedDate}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center gap-2 mb-12">
                                    <span class="w-120-px text-secondary-light fw-medium">Nominal:</span>
                                    <span class="fw-semibold text-success-600">${formattedAmount}</span>
                                </div>
                                <div class="d-flex align-items-center gap-2 mb-12">
                                    <span class="w-120-px text-secondary-light fw-medium">Status Saat Ini:</span>
                                    <span class="badge bg-primary-50 text-primary-600 fw-medium">${firstItem.new_status}</span>
                                </div>
                            </div>
                        `;
                    }

                    // Build history content with timeline style
                    let content = '';
                    if (response.DATA && response.DATA.length > 0) {
                        content = '<div class="timeline-container">';
                        response.DATA.forEach(function(item, index) {
                            const isFirst = index === 0;
                            const statusBadgeClass = getStatusBadgeClass(item.new_status);
                            const oldStatusBadge = item.old_status ?
                                `<span class="badge bg-neutral-200 text-neutral-600 fw-medium me-2">${item.old_status}</span>` :
                                '<span class="text-muted me-2">-</span>';

                            content += `
                                <div class="timeline-item ${isFirst ? 'timeline-item-current' : ''}">
                                    <div class="timeline-marker ${isFirst ? 'bg-primary-600' : 'bg-neutral-400'}">
                                        <i class="ri-arrow-right-line text-white"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex align-items-center gap-2 mb-8">
                                            ${oldStatusBadge}
                                            <i class="ri-arrow-right-line text-neutral-400"></i>
                                            <span class="badge ${statusBadgeClass} fw-medium">${item.new_status}</span>
                                        </div>
                                        <div class="text-sm text-secondary-light mb-4">
                                            <i class="ri-user-line me-1"></i>${item.changed_by_name} (${item.changed_by_role})
                                        </div>
                                        <div class="text-sm text-secondary-light mb-8">
                                            <i class="ri-time-line me-1"></i>${new Date(item.changed_date).toLocaleString('id-ID')}
                                        </div>
                                        ${item.notes ? `<div class="text-sm text-neutral-600 bg-neutral-50 p-12 radius-8"><i class="ri-chat-3-line me-1"></i>${item.notes}</div>` : ''}
                                    </div>
                                </div>
                            `;
                        });
                        content += '</div>';
                    } else {
                        content = `
                            <div class="text-center py-5">
                                <div class="mb-3">
                                    <i class="ri-history-line text-neutral-400" style="font-size: 48px;"></i>
                                </div>
                                <p class="text-secondary-light mb-0">Tidak ada history perubahan status</p>
                            </div>
                        `;
                    }

                    $('#transactionInfo').html(transactionInfo);
                    $('#historyDetailContent').html(content);
                } else {
                    $('#historyDetailContent').html(`
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ri-error-warning-line text-danger-600" style="font-size: 48px;"></i>
                            </div>
                            <p class="text-danger-600 mb-0">${response.MESSAGE}</p>
                        </div>
                    `);
                }
            },
            error: function() {
                $('#historyDetailContent').html(`
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="ri-wifi-off-line text-danger-600" style="font-size: 48px;"></i>
                        </div>
                        <p class="text-danger-600 mb-0">Terjadi kesalahan saat mengambil data</p>
                    </div>
                `);
            }
        });
    }

    function getStatusBadgeClass(status) {
        switch (status) {
            case 'Pending':
                return 'bg-warning-50 text-warning-600';
            case 'Disetujui':
                return 'bg-success-50 text-success-600';
            case 'Ditolak':
                return 'bg-danger-50 text-danger-600';
            case 'Dibatalkan':
                return 'bg-neutral-200 text-neutral-600';
            default:
                return 'bg-primary-50 text-primary-600';
        }
    }

    function exportData() {
        // Create form for export
        let form = $('<form>', {
            'method': 'POST',
            'action': '<?= base_url('transaction_history/export') ?>'
        });

        // Add current filter values
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'date_from',
            'value': $('input[name="date_from"]').val()
        }));
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'date_to',
            'value': $('input[name="date_to"]').val()
        }));
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'status',
            'value': $('select[name="status"]').val()
        }));
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'transaction_type',
            'value': $('select[name="transaction_type"]').val()
        }));

        $('body').append(form);
        form.submit();
        form.remove();
    }
</script>

<style>
    /* Timeline Styles */
    .timeline-container {
        position: relative;
        padding-left: 30px;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 24px;
        border-left: 2px solid #e5e7eb;
        margin-left: 12px;
    }

    .timeline-item:last-child {
        border-left: 2px solid transparent;
        padding-bottom: 0;
    }

    .timeline-item-current {
        border-left-color: #487fff;
    }

    .timeline-marker {
        position: absolute;
        left: -13px;
        top: 0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .timeline-marker i {
        font-size: 12px;
    }

    .timeline-content {
        padding-left: 24px;
        padding-top: 2px;
    }

    /* Modal Enhancements */
    .modal-header.bg-primary-600 {
        background-color: #487fff !important;
        border-bottom: none;
    }

    .modal-footer.bg-neutral-50 {
        background-color: #f8f9fa !important;
        border-top: 1px solid #e5e7eb;
    }

    .w-120-px {
        width: 120px;
        flex-shrink: 0;
    }

    /* Badge Styles */
    .badge {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 6px;
    }

    .bg-warning-50 {
        background-color: #fef3c7 !important;
    }

    .text-warning-600 {
        color: #d97706 !important;
    }

    .bg-success-50 {
        background-color: #dcfce7 !important;
    }

    .text-success-600 {
        color: #16a34a !important;
    }

    .bg-danger-50 {
        background-color: #fef2f2 !important;
    }

    .text-danger-600 {
        color: #dc2626 !important;
    }

    .bg-primary-50 {
        background-color: #eff6ff !important;
    }

    .text-primary-600 {
        color: #487fff !important;
    }

    .bg-neutral-50 {
        background-color: #f9fafb !important;
    }

    .bg-neutral-200 {
        background-color: #e5e7eb !important;
    }

    .text-neutral-600 {
        color: #6b7280 !important;
    }

    .text-neutral-400 {
        color: #9ca3af !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .w-120-px {
            width: 100px;
        }

        .timeline-content {
            padding-left: 16px;
        }

        .modal-body .p-24 {
            padding: 16px !important;
        }
    }
</style>