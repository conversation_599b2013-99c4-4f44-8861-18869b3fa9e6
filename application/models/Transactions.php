<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Transactions extends MY_Model
{
    protected $table = 'transactions';

    /**
     * Update transaction with status change logging
     */
    public function updateWithStatusLog($where, $data, $changed_by = null, $notes = null)
    {
        // Get current transaction data
        $current_transaction = $this->get($where)->row();

        if (!$current_transaction) {
            return false;
        }

        // Check if status is being changed
        $status_changed = false;
        $old_status = $current_transaction->status;
        $new_status = isset($data['status']) ? $data['status'] : $old_status;

        if (isset($data['status']) && $data['status'] !== $old_status) {
            $status_changed = true;
        }

        // Update the transaction
        $result = $this->update($where, $data);

        // Log status change if status was changed
        if ($result && $status_changed) {
            $CI = &get_instance();
            $CI->load->model('Transaction_status_history', 'status_history');

            $CI->status_history->logStatusChange(
                $current_transaction->id,
                $old_status,
                $new_status,
                $changed_by ?: getCurrentIdUser(),
                $notes
            );
        }

        return $result;
    }

    /**
     * Insert transaction with initial status logging
     */
    public function insertWithStatusLog($data, $created_by = null, $notes = null)
    {
        // Insert the transaction
        $transaction_id = $this->insert($data);

        if ($transaction_id) {
            // Log initial status
            $CI = &get_instance();
            $CI->load->model('Transaction_status_history', 'status_history');

            $initial_status = isset($data['status']) ? $data['status'] : 'Menunggu Pembayaran';

            $CI->status_history->logStatusChange(
                $transaction_id,
                null, // No old status for new transaction
                $initial_status,
                $created_by ?: getCurrentIdUser(),
                $notes ?: 'Transaksi dibuat'
            );
        }

        return $transaction_id;
    }

    /**
     * Delete transaction with status history cleanup
     */
    public function deleteWithStatusLog($where)
    {
        // Get transaction data first
        $transaction = $this->get($where)->row();

        if (!$transaction) {
            return false;
        }

        // Delete transaction status history
        $CI = &get_instance();
        $CI->load->model('Transaction_status_history', 'status_history');
        $CI->status_history->delete(array('transaction_id' => $transaction->id));

        // Delete the transaction
        return $this->delete($where);
    }
}
