<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Bumdes_reports extends MY_Model
{
    protected $table = 'bumdes_reports';

    /**
     * Generate kode laporan otomatis
     */
    public function generateReportCode($period = null)
    {
        if (!$period) {
            $period = date('Y');
        }

        $year = date('Y', strtotime($period));

        // Cari nomor urut terakhir untuk tahun tersebut
        $lastReport = $this->db->select('report_code')
            ->where('YEAR(report_period)', $year)
            ->where('createdby', getCurrentIdUser())
            ->order_by('id', 'DESC')
            ->limit(1)
            ->get($this->table)
            ->row();

        if ($lastReport) {
            // Extract nomor urut dari kode terakhir
            $parts = explode('-', $lastReport->report_code);
            $lastNumber = intval(end($parts));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return 'RPT-' . $year . '-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Hitung modal berdasarkan tipe
     */
    public function calculateModal($modalType, $period, $manualAmount = null)
    {
        try {
            if ($modalType === 'manual') {
                return floatval($manualAmount ?? 0);
            }

            // Ambil dari saldo awal untuk tahun tersebut
            $this->load->model('Beginningbalances', 'beginningbalances');
            $saldoAwal = $this->beginningbalances->sum('beginning_balances', array(
                'YEAR(period)' => date('Y', strtotime($period)),
                'createdby' => getCurrentIdUser(),
            )) ?? 0;

            return floatval($saldoAwal);
        } catch (Exception $e) {
            log_message('error', 'Error calculating modal: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung omset (total pendapatan) untuk periode tertentu
     * Langsung dari tabel transaksi tanpa menggunakan akun keuangan
     */
    public function calculateOmset($period)
    {
        try {
            $this->load->model('Transactions', 'transactions');

            // Query untuk mendapatkan total transaksi pendapatan dengan status Lunas
            $query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($period)))
                ->where('createdby', getCurrentIdUser())
                ->where('transactiontype', 'Pendapatan')
                ->where('status', 'Lunas')
                ->get();

            $result = $query->row();
            $omset = $result ? floatval($result->total ?? 0) : 0;

            return $omset;
        } catch (Exception $e) {
            log_message('error', 'Error calculating omset: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung keuntungan bersih dari transaksi pendapatan yang memiliki HPP
     * Rumus: keuntungan = nominal transaksi - modal/hpp
     */
    public function calculateKeuntunganWithHpp($period)
    {
        try {
            $this->load->model('Transactions', 'transactions');

            // Query untuk mendapatkan transaksi pendapatan yang memiliki HPP dengan status Lunas
            $query = $this->db->select('amount, hpp_amount')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($period)))
                ->where('createdby', getCurrentIdUser())
                ->where('transactiontype', 'Pendapatan')
                ->where('status', 'Lunas')
                ->where('hpp_amount IS NOT NULL')
                ->where('hpp_amount >', 0)
                ->get();

            $totalKeuntungan = 0;
            foreach ($query->result() as $transaction) {
                $keuntungan = floatval($transaction->amount) - floatval($transaction->hpp_amount);
                $totalKeuntungan += $keuntungan;
            }

            return $totalKeuntungan;
        } catch (Exception $e) {
            log_message('error', 'Error calculating keuntungan with HPP: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung total HPP untuk periode tertentu
     */
    public function calculateTotalHpp($period)
    {
        try {
            $this->load->model('Transactions', 'transactions');

            // Query untuk mendapatkan total HPP dari transaksi pendapatan dengan status Lunas
            $query = $this->db->select('SUM(hpp_amount) as total')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($period)))
                ->where('createdby', getCurrentIdUser())
                ->where('transactiontype', 'Pendapatan')
                ->where('status', 'Lunas')
                ->where('hpp_amount IS NOT NULL')
                ->where('hpp_amount >', 0)
                ->get();

            $result = $query->row();
            $totalHpp = $result ? floatval($result->total ?? 0) : 0;

            return $totalHpp;
        } catch (Exception $e) {
            log_message('error', 'Error calculating total HPP: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung omset dari transaksi pendapatan yang tidak memiliki HPP
     */
    public function calculateOmsetTanpaHpp($period)
    {
        try {
            $this->load->model('Transactions', 'transactions');

            // Query untuk mendapatkan total transaksi pendapatan yang tidak memiliki HPP dengan status Lunas
            $query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($period)))
                ->where('createdby', getCurrentIdUser())
                ->where('transactiontype', 'Pendapatan')
                ->where('status', 'Lunas')
                ->group_start()
                ->where('hpp_amount IS NULL')
                ->or_where('hpp_amount', 0)
                ->group_end()
                ->get();

            $result = $query->row();
            $omsetTanpaHpp = $result ? floatval($result->total ?? 0) : 0;

            return $omsetTanpaHpp;
        } catch (Exception $e) {
            log_message('error', 'Error calculating omset tanpa HPP: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung total pengeluaran untuk periode tertentu
     * Langsung dari tabel transaksi tanpa menggunakan akun keuangan
     */
    public function calculatePengeluaran($period)
    {
        try {
            $this->load->model('Transactions', 'transactions');

            // Query untuk mendapatkan total transaksi pengeluaran dengan status Lunas
            $query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($period)))
                ->where('createdby', getCurrentIdUser())
                ->where('transactiontype', 'Pengeluaran')
                ->where('status', 'Lunas')
                ->get();

            $result = $query->row();
            $pengeluaran = $result ? floatval($result->total ?? 0) : 0;

            return $pengeluaran;
        } catch (Exception $e) {
            log_message('error', 'Error calculating pengeluaran: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung laba berdasarkan tipe
     */
    public function calculateLaba($labaType, $period, $manualAmount = null)
    {
        try {
            if ($labaType === 'manual') {
                return floatval($manualAmount ?? 0);
            }

            // Cek apakah ada transaksi dengan HPP
            $totalHpp = $this->calculateTotalHpp($period);

            if ($totalHpp > 0) {
                // Jika ada HPP, gunakan rumus: keuntungan dari transaksi ber-HPP + (pendapatan tanpa HPP - pengeluaran)
                $keuntunganWithHpp = $this->calculateKeuntunganWithHpp($period);
                $pendapatanTanpaHpp = $this->calculateOmsetTanpaHpp($period);
                $pengeluaran = $this->calculatePengeluaran($period);

                return floatval($keuntunganWithHpp + ($pendapatanTanpaHpp - $pengeluaran));
            } else {
                // Jika tidak ada HPP, gunakan rumus lama: pendapatan - pengeluaran
                $pendapatan = $this->calculateOmset($period);
                $pengeluaran = $this->calculatePengeluaran($period);

                return floatval($pendapatan - $pengeluaran);
            }
        } catch (Exception $e) {
            log_message('error', 'Error calculating laba: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Hitung balance check (modal + laba)
     */
    public function calculateBalanceCheck($modal, $laba)
    {
        return $modal + $laba;
    }

    /**
     * Validasi apakah laporan untuk periode tersebut sudah ada
     */
    public function isReportExists($period, $userId = null)
    {
        if (!$userId) {
            $userId = getCurrentIdUser();
        }

        // Untuk periode tahunan, cek berdasarkan tahun
        $year = date('Y', strtotime($period));
        $count = $this->db->where('YEAR(report_period)', $year)
            ->where('createdby', $userId)
            ->count_all_results($this->table);

        return $count > 0;
    }

    /**
     * Get laporan dengan join data user
     */
    public function getReportsWithUser($where = array(), $limit = null, $offset = null)
    {
        // Cek apakah kolom desaname ada
        $has_desa_columns = $this->checkDesaColumns();

        if ($has_desa_columns) {
            $this->db->select('a.*, u.name as bumdes_name, u.desaname, u.kecamatanname, u.kabkotaname');
        } else {
            $this->db->select('a.*, u.name as bumdes_name, "" as desaname, "" as kecamatanname, "" as kabkotaname');
        }

        $this->db->from($this->table . ' a')
            ->join('msusers u', 'u.id = a.createdby', 'left');

        if (!empty($where)) {
            $this->db->where($where);
        }

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        $this->db->order_by('a.report_period', 'DESC');

        return $this->db->get();
    }

    /**
     * Cek apakah kolom desa ada di tabel msusers
     */
    private function checkDesaColumns()
    {
        $query = $this->db->query("SHOW COLUMNS FROM msusers LIKE 'desaname'");
        return $query->num_rows() > 0;
    }

    /**
     * Get statistik laporan untuk dashboard
     */
    public function getReportStatistics($userId = null, $filters = array())
    {
        if (!$userId) {
            $userId = getCurrentIdUser();
        }

        $currentYear = isset($filters['year']) ? $filters['year'] : date('Y');

        $stats = array();

        // Base where conditions
        $baseWhere = array(
            'YEAR(report_period)' => $currentYear,
            'createdby' => $userId
        );

        // Total laporan
        $this->db->where($baseWhere);
        $stats['total_reports'] = $this->db->count_all_results($this->table);

        // Total modal
        $this->db->select('SUM(modal_amount) as total');
        $this->db->where($baseWhere);
        $stats['total_modal'] = $this->db->get($this->table)->row()->total ?? 0;

        // Total omset
        $this->db->select('SUM(omset_amount) as total');
        $this->db->where($baseWhere);
        $stats['total_omset'] = $this->db->get($this->table)->row()->total ?? 0;

        // Total laba
        $this->db->select('SUM(laba_amount) as total');
        $this->db->where($baseWhere);
        $stats['total_laba'] = $this->db->get($this->table)->row()->total ?? 0;

        return $stats;
    }

    /**
     * Get laporan untuk Super Admin (semua BUMDes)
     */
    public function getAllReportsForSuperAdmin($where = array(), $limit = null, $offset = null)
    {
        // Cek apakah kolom desaname ada
        $has_desa_columns = $this->checkDesaColumns();

        if ($has_desa_columns) {
            $this->db->select('a.*, u.name as bumdes_name, u.desaname, u.kecamatanname, u.kabkotaname');
        } else {
            $this->db->select('a.*, u.name as bumdes_name, "" as desaname, "" as kecamatanname, "" as kabkotaname');
        }

        $this->db->from($this->table . ' a')
            ->join('msusers u', 'u.id = a.createdby', 'left')
            ->where('u.role', 'BUMDes');

        if (!empty($where)) {
            $this->db->where($where);
        }

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        $this->db->order_by('a.report_period', 'DESC');

        return $this->db->get();
    }

    /**
     * Get statistik untuk Super Admin
     */
    public function getSuperAdminStatistics($filters = array())
    {
        $currentYear = isset($filters['year']) ? $filters['year'] : date('Y');
        $bumdes_id = isset($filters['bumdes_id']) ? $filters['bumdes_id'] : null;

        $stats = array();

        // Base where conditions
        $baseWhere = array(
            'YEAR(a.report_period)' => $currentYear,
            'u.role' => 'BUMDes'
        );

        // Add BUMDes filter if specified
        if ($bumdes_id) {
            $baseWhere['a.createdby'] = $bumdes_id;
        }

        // Total laporan
        $this->db->select('COUNT(*) as total');
        $this->db->from($this->table . ' a');
        $this->db->join('msusers u', 'u.id = a.createdby', 'left');
        $this->db->where($baseWhere);
        $stats['total_reports'] = $this->db->get()->row()->total ?? 0;

        // Total BUMDes yang memiliki laporan
        $this->db->select('COUNT(DISTINCT a.createdby) as total');
        $this->db->from($this->table . ' a');
        $this->db->join('msusers u', 'u.id = a.createdby', 'left');
        $this->db->where($baseWhere);
        $stats['active_bumdes'] = $this->db->get()->row()->total ?? 0;

        // Total modal
        $this->db->select('SUM(a.modal_amount) as total');
        $this->db->from($this->table . ' a');
        $this->db->join('msusers u', 'u.id = a.createdby', 'left');
        $this->db->where($baseWhere);
        $stats['total_modal_all'] = $this->db->get()->row()->total ?? 0;

        // Total omset
        $this->db->select('SUM(a.omset_amount) as total');
        $this->db->from($this->table . ' a');
        $this->db->join('msusers u', 'u.id = a.createdby', 'left');
        $this->db->where($baseWhere);
        $stats['total_omset_all'] = $this->db->get()->row()->total ?? 0;

        // Total laba
        $this->db->select('SUM(a.laba_amount) as total');
        $this->db->from($this->table . ' a');
        $this->db->join('msusers u', 'u.id = a.createdby', 'left');
        $this->db->where($baseWhere);
        $stats['total_laba_all'] = $this->db->get()->row()->total ?? 0;

        // Alias untuk kompatibilitas dengan view
        $stats['total_laba'] = $stats['total_laba_all'];

        return $stats;
    }
}
