<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Bumdes_user_model extends MY_Model
{
    protected $table = 'msbumdesusers';

    /**
     * Get pengguna BUMDes dengan join data BUMDes dan unit kerja
     */
    public function getUsersWithDetails($where = array(), $limit = null, $offset = null)
    {
        $this->db->select('a.*, b.name as bumdes_name, b.businessname, c.workunitcode, c.workunitname, d.name as creator_name')
            ->from($this->table . ' a')
            ->join('msusers b', 'a.bumdes_id = b.id', 'left')
            ->join('msworkunits c', 'a.workunitid = c.id', 'left')
            ->join('msusers d', 'a.createdby = d.id', 'left');

        if (!empty($where)) {
            $this->db->where($where);
        }

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        $this->db->order_by('a.createddate', 'DESC');

        return $this->db->get();
    }

    /**
     * Get pengguna BUMDes berdasarkan BUMDes ID
     */
    public function getUsersByBumdesId($bumdes_id, $where = array())
    {
        $where['a.bumdes_id'] = $bumdes_id;
        return $this->getUsersWithDetails($where);
    }

    /**
     * Cek apakah username sudah digunakan
     */
    public function isUsernameExists($username, $exclude_id = null)
    {
        $this->db->where('username', $username);

        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Get pengguna untuk login
     */
    public function getUserForLogin($username)
    {
        return $this->db->select('a.*, b.name as bumdes_name, b.businessname, c.workunitcode, c.workunitname')
            ->from($this->table . ' a')
            ->join('msusers b', 'a.bumdes_id = b.id', 'left')
            ->join('msworkunits c', 'a.workunitid = c.id', 'left')
            ->where('a.username', $username)
            ->where('a.status', 'Aktif')
            ->get()
            ->row();
    }

    /**
     * Get statistik pengguna BUMDes
     */
    public function getUserStatistics($bumdes_id = null)
    {
        $stats = array();

        // Base where condition
        $where = array();
        if ($bumdes_id) {
            $where['bumdes_id'] = $bumdes_id;
        }

        // Total pengguna aktif
        $where_aktif = $where;
        $where_aktif['status'] = 'Aktif';
        $stats['total_aktif'] = $this->total($where_aktif);

        // Total pengguna nonaktif
        $where_nonaktif = $where;
        $where_nonaktif['status'] = 'Nonaktif';
        $stats['total_nonaktif'] = $this->total($where_nonaktif);

        // Total semua pengguna
        $stats['total_all'] = $this->total($where);

        return $stats;
    }

    /**
     * Update status pengguna
     */
    public function updateStatus($id, $status, $updated_by = null)
    {
        $update_data = array(
            'status' => $status,
            'updatedby' => $updated_by ?: getCurrentIdUser(),
            'updateddate' => getCurrentDate()
        );

        return $this->update(array('id' => $id), $update_data);
    }

    /**
     * Log aktivitas pengguna
     */
    public function logActivity($user_id, $activity, $description = null)
    {
        $CI = &get_instance();

        // Check if activity log table exists
        if (!$CI->db->table_exists('user_activity_logs')) {
            return false;
        }

        $log_data = array(
            'user_id' => $user_id,
            'activity' => $activity,
            'description' => $description,
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent(),
            'created_at' => getCurrentDate()
        );

        return $CI->db->insert('user_activity_logs', $log_data);
    }

    /**
     * Get recent activities
     */
    public function getRecentActivities($bumdes_id, $limit = 10)
    {
        $CI = &get_instance();

        if (!$CI->db->table_exists('user_activity_logs')) {
            return array();
        }

        return $CI->db->select('l.*, u.name as user_name')
            ->from('user_activity_logs l')
            ->join('msbumdesusers u', 'l.user_id = u.id', 'left')
            ->where('u.bumdes_id', $bumdes_id)
            ->order_by('l.created_at', 'DESC')
            ->limit($limit)
            ->get()
            ->result();
    }
}
