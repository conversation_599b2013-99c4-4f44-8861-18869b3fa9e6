<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Superadmins $superadmins
 * @property CI_Form_validation $form_validation
 */
class Auth extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Superadmins', 'superadmins');
        $this->load->model('Bumdes_user_model', 'bumdes_users');
        $this->load->helper('security');
    }

    public function logout()
    {
        // Log logout event before destroying session
        if (isLogin()) {
            $user_id = getCurrentIdUser();
            $username = getSessionValue('USERNAME');
            $role = getSessionValue('ROLE');

            logSecurityEvent('USER_LOGOUT', [
                'user_id' => $user_id,
                'username' => $username,
                'user_type' => $role
            ], 'LOW', $user_id);
        }

        destroySession();

        return redirect(base_url('auth/login'));
    }

    public function login()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard'));
        }

        return $this->load->view('auth/login');
    }

    public function process_login()
    {
        if (isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda sudah login.');
        }

        $username = sanitizeInput(getPost('username'));
        $password = getPost('password');

        $this->form_validation->set_rules('username', 'Username', array('required', 'trim'));
        $this->form_validation->set_rules('password', 'Password', array('required', 'trim'));

        if ($this->form_validation->run() == false) {
            logLoginAttempt($username, false, 'Validation failed: ' . strip_tags(validation_errors()));
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        // Check login attempts rate limiting
        if (!checkLoginAttempts($username)) {
            logSecurityEvent('LOGIN_RATE_LIMIT_EXCEEDED', [
                'username' => $username,
                'ip_address' => $this->input->ip_address()
            ], 'HIGH');
            return JSONResponseDefault('FAILED', 'Terlalu banyak percobaan login. Silakan coba lagi dalam 15 menit.');
        }

        // Cek di tabel msusers (Super Admin, BUMDes, dll)
        $user = $this->superadmins->get(array('username' => $username))->row();

        // Jika tidak ditemukan di msusers, cek di tabel msbumdesusers (Pengguna BUMDes)
        if ($user == null) {
            $bumdes_user = $this->bumdes_users->getUserForLogin($username);

            if ($bumdes_user != null) {
                // Verifikasi password untuk pengguna BUMDes
                if (password_verify($password, $bumdes_user->password)) {
                    setSessionValue(array(
                        'ID' => $bumdes_user->id,
                        'USERNAME' => $bumdes_user->username,
                        'NAME' => $bumdes_user->name,
                        'ROLE' => 'BUMDes User',
                        'BUMDES_ID' => $bumdes_user->bumdes_id,
                        'WORKUNIT_ID' => $bumdes_user->workunitid,
                        'BUMDES_NAME' => $bumdes_user->bumdes_name,
                        'WORKUNIT_NAME' => $bumdes_user->workunitname,
                    ));

                    // Log successful login
                    logLoginAttempt($username, true, 'BUMDes User login successful');
                    logSecurityEvent('USER_LOGIN', [
                        'user_id' => $bumdes_user->id,
                        'user_type' => 'BUMDes User',
                        'username' => $username,
                        'bumdes_id' => $bumdes_user->bumdes_id,
                        'workunit_id' => $bumdes_user->workunitid
                    ], 'LOW', $bumdes_user->id);

                    return JSONResponseDefault('OK', 'Login berhasil.');
                }
            }

            // Log failed login attempt
            logLoginAttempt($username, false, 'Invalid credentials');
            logSecurityEvent('LOGIN_FAILED', [
                'username' => $username,
                'reason' => 'Invalid credentials'
            ], 'MEDIUM');

            return JSONResponseDefault('FAILED', 'Username atau password yang Anda masukkan salah.');
        }

        // Verifikasi password untuk user biasa (Super Admin, BUMDes, dll)
        if (!password_verify($password, $user->password)) {
            // Log failed login attempt
            logLoginAttempt($username, false, 'Invalid password for existing user');
            logSecurityEvent('LOGIN_FAILED', [
                'username' => $username,
                'user_id' => $user->id,
                'reason' => 'Invalid password'
            ], 'MEDIUM');

            return JSONResponseDefault('FAILED', 'Username atau password yang Anda masukkan salah.');
        }

        // Check if BUMDes account is verified (status = Aktif)
        if ($user->role === 'BUMDes' && isset($user->status) && $user->status === 'Pending') {
            logLoginAttempt($username, false, 'Account pending verification');
            logSecurityEvent('LOGIN_FAILED', [
                'username' => $username,
                'user_id' => $user->id,
                'reason' => 'Account pending verification'
            ], 'LOW');

            return JSONResponseDefault('FAILED', 'Akun Anda sedang menunggu verifikasi dari Super Admin. Silakan hubungi administrator untuk aktivasi akun.');
        }

        // Check if account is inactive
        if (isset($user->status) && $user->status === 'Nonaktif') {
            logLoginAttempt($username, false, 'Account inactive');
            logSecurityEvent('LOGIN_FAILED', [
                'username' => $username,
                'user_id' => $user->id,
                'reason' => 'Account inactive'
            ], 'LOW');

            return JSONResponseDefault('FAILED', 'Akun Anda telah dinonaktifkan. Silakan hubungi administrator.');
        }

        setSessionValue(array(
            'ID' => $user->id,
            'USERNAME' => $user->username,
            'NAME' => $user->name,
            'ROLE' => $user->role,
        ));

        // Log successful login
        logLoginAttempt($username, true, 'Standard user login successful');
        logSecurityEvent('USER_LOGIN', [
            'user_id' => $user->id,
            'user_type' => $user->role,
            'username' => $username
        ], 'LOW', $user->id);

        return JSONResponseDefault('OK', 'Login berhasil.');
    }
}
