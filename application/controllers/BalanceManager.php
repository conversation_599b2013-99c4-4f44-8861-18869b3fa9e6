<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Beginningbalances $beginningbalances
 * @property BalanceHistory $balancehistory
 * @property CI_Form_validation $form_validation
 */
class BalanceManager extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Beginningbalances', 'beginningbalances');
        $this->load->model('BalanceHistory', 'balancehistory');
    }

    /**
     * Tambah saldo awal
     */
    public function add_balance()
    {
        if (!isLogin()) {
            JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
            return;
        }

        if (isSuperAdmin()) {
            JSONResponseDefault('FAILED', 'Super Admin tidak dapat mengubah saldo awal.');
            return;
        }

        $beginningbalance_id = getPost('beginningbalance_id');
        $amount = getPost('amount');
        $description = getPost('description');

        $this->form_validation->set_rules('beginningbalance_id', 'ID Saldo Awal', 'required|numeric');
        $this->form_validation->set_rules('amount', 'Nominal', 'required|numeric|greater_than[0]');

        if ($this->form_validation->run() === false) {
            JSONResponseDefault('FAILED', strip_tags(validation_errors()));
            return;
        }

        // Cek apakah saldo awal ada dan milik user yang login
        $balance = $this->beginningbalances->get(array(
            'id' => $beginningbalance_id,
            'createdby' => getCurrentIdUser()
        ))->row();

        if (!$balance) {
            JSONResponseDefault('FAILED', 'Data saldo awal tidak ditemukan.');
            return;
        }

        $this->db->trans_start();

        try {
            // Update saldo awal
            $new_balance = $balance->beginning_balances + $amount;

            // Debug: Log values
            log_message('debug', 'Current balance: ' . $balance->beginning_balances);
            log_message('debug', 'Amount to add: ' . $amount);
            log_message('debug', 'New balance: ' . $new_balance);
            log_message('debug', 'Balance ID: ' . $beginningbalance_id);

            $update_result = $this->beginningbalances->update(array('id' => $beginningbalance_id), array(
                'beginning_balances' => $new_balance
            ));

            log_message('debug', 'Update result: ' . ($update_result ? 'TRUE' : 'FALSE'));

            // Simpan riwayat
            $history_data = array(
                'beginningbalance_id' => $beginningbalance_id,
                'amount' => $amount,
                'type' => 'Tambah',
                'description' => $description,
                'createdby' => getCurrentIdUser(),
                'createddate' => getCurrentDate()
            );
            $this->balancehistory->insert($history_data);

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                JSONResponseDefault('FAILED', 'Gagal menambah saldo awal.');
                return;
            }

            JSONResponseDefault('OK', 'Saldo awal berhasil ditambahkan.');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            JSONResponseDefault('FAILED', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Kurangi saldo awal
     */
    public function reduce_balance()
    {
        if (!isLogin()) {
            JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
            return;
        }

        if (isSuperAdmin()) {
            JSONResponseDefault('FAILED', 'Super Admin tidak dapat mengubah saldo awal.');
            return;
        }

        $beginningbalance_id = getPost('beginningbalance_id');
        $amount = getPost('amount');
        $description = getPost('description');

        $this->form_validation->set_rules('beginningbalance_id', 'ID Saldo Awal', 'required|numeric');
        $this->form_validation->set_rules('amount', 'Nominal', 'required|numeric|greater_than[0]');

        if ($this->form_validation->run() === false) {
            JSONResponseDefault('FAILED', strip_tags(validation_errors()));
            return;
        }

        // Cek apakah saldo awal ada dan milik user yang login
        $balance = $this->beginningbalances->get(array(
            'id' => $beginningbalance_id,
            'createdby' => getCurrentIdUser()
        ))->row();

        if (!$balance) {
            JSONResponseDefault('FAILED', 'Data saldo awal tidak ditemukan.');
            return;
        }

        // Cek apakah saldo mencukupi
        if ($balance->beginning_balances < $amount) {
            JSONResponseDefault('FAILED', 'Saldo tidak mencukupi untuk dikurangi.');
            return;
        }

        $this->db->trans_start();

        try {
            // Update saldo awal
            $new_balance = $balance->beginning_balances - $amount;

            // Debug: Log values
            log_message('debug', 'Current balance: ' . $balance->beginning_balances);
            log_message('debug', 'Amount to reduce: ' . $amount);
            log_message('debug', 'New balance: ' . $new_balance);
            log_message('debug', 'Balance ID: ' . $beginningbalance_id);

            $update_result = $this->beginningbalances->update(array('id' => $beginningbalance_id), array(
                'beginning_balances' => $new_balance
            ));

            log_message('debug', 'Update result: ' . ($update_result ? 'TRUE' : 'FALSE'));

            // Simpan riwayat
            $history_data = array(
                'beginningbalance_id' => $beginningbalance_id,
                'amount' => $amount,
                'type' => 'Kurang',
                'description' => $description,
                'createdby' => getCurrentIdUser(),
                'createddate' => getCurrentDate()
            );
            $this->balancehistory->insert($history_data);

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                JSONResponseDefault('FAILED', 'Gagal mengurangi saldo awal.');
                return;
            }

            JSONResponseDefault('OK', 'Saldo awal berhasil dikurangi.');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            JSONResponseDefault('FAILED', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Get riwayat saldo
     */
    public function get_balance_history($beginningbalance_id)
    {
        if (!isLogin()) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        // Cek apakah saldo awal ada dan milik user yang login
        $balance = $this->beginningbalances->get(array(
            'id' => $beginningbalance_id,
            'createdby' => getCurrentIdUser()
        ))->row();

        if (!$balance) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Data saldo awal tidak ditemukan.'
            ));
        }

        // Get balance info with work unit data
        $balance_with_workunit = $this->beginningbalances->select('a.*, CONCAT(b.workunitcode, " - ", b.workunitname) as workunitdata')
            ->join('msworkunits b', 'a.workunitid = b.id')
            ->get(array('a.id' => $beginningbalance_id))
            ->row();

        // Get history
        $history = $this->balancehistory->select('a.*')
            ->order_by('a.createddate', 'DESC')
            ->result(array('a.beginningbalance_id' => $beginningbalance_id));

        $data = array(
            'balance_info' => $balance_with_workunit,
            'history' => $history
        );

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil.',
            'DATA' => $data
        ));
    }
}
