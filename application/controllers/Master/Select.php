<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsCities $mscities
 * @property MsDistricts $msdistricts
 * @property MsVillages $msvillages
 */
class Select extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsCities', 'mscities');
        $this->load->model('MsDistricts', 'msdistricts');
        $this->load->model('MsVillages', 'msvillages');
    }

    public function cities()
    {
        $code = getPost('code');
        $selected = getPost('selected');

        $cities = $this->mscities->where('province_code', $code)->order_by('name', 'ASC')->result();

        $select = "";
        foreach ($cities as $city) {
            if ($selected == $city->code) {
                $select .= "<option value='{$city->code}' selected>{$city->name}</option>";
                continue;
            }

            $select .= "<option value='{$city->code}'>{$city->name}</option>";
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $select,
            'MESSAGE' => ''
        ));
    }

    public function districts()
    {
        $code = getPost('code');
        $selected = getPost('selected');

        $districts = $this->msdistricts->where('city_code', $code)->order_by('name', 'ASC')->result();

        $select = "";
        foreach ($districts as $district) {
            if ($selected == $district->code) {
                $select .= "<option value='{$district->code}' selected>{$district->name}</option>";
                continue;
            }

            $select .= "<option value='{$district->code}'>{$district->name}</option>";
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $select,
            'MESSAGE' => ''
        ));
    }

    public function villages()
    {
        $code = getPost('code');
        $selected = getPost('selected');

        $villages = $this->msvillages->where('district_code', $code)->order_by('name', 'ASC')->result();

        $select = "";
        foreach ($villages as $village) {
            if ($selected == $village->code) {
                $select .= "<option value='{$village->code}' selected>{$village->name}</option>";
                continue;
            }

            $select .= "<option value='{$village->code}'>{$village->name}</option>";
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $select,
            'MESSAGE' => ''
        ));
    }
}
