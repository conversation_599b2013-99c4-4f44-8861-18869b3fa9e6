<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Bumdes_users extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Bumdes_user_model', 'bumdes_users');
        $this->load->model('Superadmins', 'superadmins');
        $this->load->model('Workunits', 'workunits');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Hanya BUMDes yang bisa akses fitur ini
        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            show_404();
        }

        $data = array();
        $data['title'] = 'Manajemen Pengguna';
        $data['content'] = 'bumdes_users/index';

        // Get pengguna untuk BUMDes yang sedang login
        $bumdes_id = getCurrentIdUser();
        $data['users'] = $this->bumdes_users->getUsersByBumdesId($bumdes_id)->result();
        $data['statistics'] = $this->bumdes_users->getUserStatistics($bumdes_id);

        // Get unit kerja yang tersedia untuk BUMDes ini untuk filter
        $bumdes_workunit_ids = explode(',', $current_user->workunitid);
        $data['workunits'] = $this->workunits->select('*')
            ->where_in('id', $bumdes_workunit_ids)
            ->order_by('workunitname', 'ASC')
            ->result();

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            show_404();
        }

        $data = array();
        $data['title'] = 'Tambah Pengguna';
        $data['content'] = 'bumdes_users/add';

        // Get unit kerja yang tersedia untuk BUMDes ini
        $bumdes_workunit_ids = explode(',', $current_user->workunitid);
        $data['workunits'] = $this->workunits->select('*')
            ->where_in('id', $bumdes_workunit_ids)
            ->order_by('workunitname', 'ASC')
            ->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $name = getPost('name');
        $phone = getPost('phone');
        $address = getPost('address');
        $username = getPost('username');
        $password = getPost('password');
        $workunitid = getPost('workunitid');

        // Validasi input
        $this->form_validation->set_rules('name', 'Nama', 'required|trim');
        $this->form_validation->set_rules('username', 'Username', 'required|trim|min_length[4]');
        $this->form_validation->set_rules('password', 'Password', 'required|trim|min_length[6]');
        $this->form_validation->set_rules('workunitid', 'Unit Kerja', 'required|numeric');

        if ($this->form_validation->run() == false) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => strip_tags(validation_errors())
            ));
        }

        // Cek apakah username sudah digunakan
        if ($this->bumdes_users->isUsernameExists($username)) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Username sudah digunakan.'
            ));
        }

        // Validasi unit kerja harus milik BUMDes yang sedang login
        $bumdes_workunit_ids = explode(',', $current_user->workunitid);
        if (!in_array($workunitid, $bumdes_workunit_ids)) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Unit kerja tidak valid.'
            ));
        }

        $insert = array();
        $insert['bumdes_id'] = getCurrentIdUser();
        $insert['name'] = $name;
        $insert['phone'] = $phone;
        $insert['address'] = $address;
        $insert['username'] = $username;
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['workunitid'] = $workunitid;
        $insert['status'] = 'Aktif';
        $insert['createdby'] = getCurrentIdUser();
        $insert['createddate'] = getCurrentDate();

        $this->bumdes_users->insert($insert);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil disimpan.'
        ));
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            show_404();
        }

        // Cek apakah pengguna ini milik BUMDes yang sedang login
        $user = $this->bumdes_users->get(array('id' => $id, 'bumdes_id' => getCurrentIdUser()))->row();
        if (!$user) {
            show_404();
        }

        $data = array();
        $data['title'] = 'Edit Pengguna';
        $data['content'] = 'bumdes_users/edit';
        $data['user'] = $user;

        // Get unit kerja yang tersedia untuk BUMDes ini
        $bumdes_workunit_ids = explode(',', $current_user->workunitid);
        $data['workunits'] = $this->workunits->select('*')
            ->where_in('id', $bumdes_workunit_ids)
            ->order_by('workunitname', 'ASC')
            ->result();

        return $this->load->view('master', $data);
    }

    public function process_edit()
    {
        if (!isLogin()) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $id = getPost('id');
        $name = getPost('name');
        $phone = getPost('phone');
        $address = getPost('address');
        $username = getPost('username');
        $password = getPost('password');
        $workunitid = getPost('workunitid');

        // Validasi input
        $this->form_validation->set_rules('id', 'ID', 'required|numeric');
        $this->form_validation->set_rules('name', 'Nama', 'required|trim');
        $this->form_validation->set_rules('username', 'Username', 'required|trim|min_length[4]');
        $this->form_validation->set_rules('workunitid', 'Unit Kerja', 'required|numeric');

        if ($this->form_validation->run() == false) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => strip_tags(validation_errors())
            ));
        }

        // Cek apakah pengguna ini milik BUMDes yang sedang login
        $user = $this->bumdes_users->get(array('id' => $id, 'bumdes_id' => getCurrentIdUser()))->row();
        if (!$user) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Data tidak ditemukan.'
            ));
        }

        // Cek apakah username sudah digunakan (kecuali untuk user ini sendiri)
        if ($this->bumdes_users->isUsernameExists($username, $id)) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Username sudah digunakan.'
            ));
        }

        // Validasi unit kerja harus milik BUMDes yang sedang login
        $bumdes_workunit_ids = explode(',', $current_user->workunitid);
        if (!in_array($workunitid, $bumdes_workunit_ids)) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Unit kerja tidak valid.'
            ));
        }

        $update = array();
        $update['name'] = $name;
        $update['phone'] = $phone;
        $update['address'] = $address;
        $update['username'] = $username;
        $update['workunitid'] = $workunitid;
        $update['updatedby'] = getCurrentIdUser();
        $update['updateddate'] = getCurrentDate();

        // Update password jika diisi
        if (!empty($password)) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $this->bumdes_users->update(array('id' => $id), $update);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil disimpan.'
        ));
    }

    public function delete()
    {
        if (!isLogin()) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $id = getPost('id');

        // Cek apakah pengguna ini milik BUMDes yang sedang login
        $user = $this->bumdes_users->get(array('id' => $id, 'bumdes_id' => getCurrentIdUser()))->row();
        if (!$user) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Data tidak ditemukan.'
            ));
        }

        $this->bumdes_users->delete(array('id' => $id));

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil dihapus.'
        ));
    }

    public function toggle_status()
    {
        if (!isLogin()) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Anda tidak memiliki akses.'
            ));
        }

        $id = getPost('id');
        $status = getPost('status');

        // Validasi status
        if (!in_array($status, ['Aktif', 'Nonaktif'])) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Status tidak valid.'
            ));
        }

        // Cek apakah pengguna ini milik BUMDes yang sedang login
        $user = $this->bumdes_users->get(array('id' => $id, 'bumdes_id' => getCurrentIdUser()))->row();
        if (!$user) {
            return JSONResponse(array(
                'RESULT' => 'FAILED',
                'MESSAGE' => 'Data tidak ditemukan.'
            ));
        }

        $this->bumdes_users->updateStatus($id, $status, getCurrentIdUser());

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Status berhasil diubah.'
        ));
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $current_user = getCurrentUser();
        if ($current_user->role !== 'BUMDes') {
            show_404();
        }

        // Get pengguna untuk BUMDes yang sedang login
        $bumdes_id = getCurrentIdUser();
        $users = $this->bumdes_users->getUsersByBumdesId($bumdes_id)->result();

        // Set headers untuk download CSV
        $filename = 'data_pengguna_bumdes_' . date('Y-m-d_H-i-s') . '.csv';
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);

        // Create output stream
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF) . chr(0xBB) . chr(0xBF));

        // CSV headers
        fputcsv($output, [
            'No',
            'Nama',
            'Username',
            'No. WhatsApp',
            'Alamat',
            'Unit Kerja',
            'Status',
            'Tanggal Dibuat',
            'Dibuat Oleh'
        ]);

        // CSV data
        $no = 1;
        foreach ($users as $user) {
            fputcsv($output, [
                $no++,
                $user->name,
                $user->username,
                $user->phone ?: '-',
                $user->address ?: '-',
                $user->workunitcode . ' - ' . $user->workunitname,
                $user->status,
                date('d/m/Y H:i', strtotime($user->createddate)),
                $user->creator_name ?: '-'
            ]);
        }

        fclose($output);
        exit;
    }
}
