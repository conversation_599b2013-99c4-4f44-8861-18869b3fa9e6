<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**

 * @property Transactions $transactions
 * @property CI_Form_validation $form_validation
 * @property CI_DB_mysqli_driver $db
 */
class Transaction extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();


        $this->load->model('Transactions', 'transactions');
        $this->load->model('Beginningbalances', 'beginningbalances');
        $this->load->library('crud');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Daftar Transaksi';
        $data['content'] = 'crudcore/index';

        // Set feature in crud library
        $this->crud->setFeature('transaction');

        // Get base configuration
        $config = getContents('transaction', 'index');

        // Modify database configuration based on user role
        if (isBumdes()) {
            // BUMDes access: include transactions from village BUMDes users
            $village_id = getVillageId();
            $current_user_id = getCurrentIdUser();

            // Get all BUMDes users from the same village
            $village_bumdes_users = array();
            if ($village_id) {
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                    ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                    ->result(array('b.villageid' => $village_id));

                foreach ($bumdes_users_in_village as $user) {
                    $village_bumdes_users[] = $user->id;
                }
            }

            // Include current BUMDes and all BUMDes users from same village
            $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

            // Get transactions with custom filtering including work unit data
            $transactions = $this->transactions->select('a.*, COALESCE(CONCAT(b.workunitcode, " - ", b.workunitname), "N/A") as workunitdata')
                ->join('msworkunits b', 'a.workunitid = b.id', 'LEFT')
                ->where_in('a.createdby', $allowed_creators)
                ->order_by('a.transactiondate', 'DESC')
                ->get()
                ->result();

            $config['result'] = $transactions;
        } else {
            // Use default CRUD behavior for other roles (work unit join is now handled in configuration)
            $config = $this->crud->getElements($config);
        }

        // Add feature identifier for filters (must be after getElements to avoid being overwritten)
        $config['feature'] = 'transaction';

        $data['element'] = $config;



        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah Transaksi';
        $data['content'] = 'transaction/add';


        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        // Validasi input required
        if (empty(getPost('workunitid'))) {
            return JSONResponseDefault('FAILED', 'Unit Usaha harus dipilih.');
        }

        // Validasi akses untuk pengguna BUMDes
        if (isBumdesUser()) {
            $workunitid = getPost('workunitid');
            // Pengguna BUMDes hanya bisa menambah transaksi untuk unit usahanya sendiri
            if ($workunitid != getWorkunitId()) {
                return JSONResponseDefault('FAILED', 'Anda hanya dapat menambah transaksi untuk unit usaha Anda.');
            }
        }

        // Validasi saldo awal harus ada untuk bulan transaksi
        $transactionDate = getPost('transactiondate');

        // Tentukan createdby berdasarkan role
        $createdby = isBumdesUser() ? getBumdesId() : getCurrentIdUser();

        // Cek apakah ada saldo awal untuk tahun tersebut
        // Saldo awal disimpan per tahun, bukan per bulan
        $where_balance = array(
            'YEAR(period)' => date('Y', strtotime($transactionDate)),
            'createdby' => $createdby
        );

        // Untuk pengguna BUMDes, tambahkan filter unit usaha
        if (isBumdesUser()) {
            $where_balance['workunitid'] = getWorkunitId();
        }

        $beginningBalance = $this->beginningbalances->get($where_balance);

        if ($beginningBalance->num_rows() == 0) {
            $error_msg = 'Saldo awal untuk tahun ' . date('Y', strtotime($transactionDate));
            if (isBumdesUser()) {
                $error_msg .= ' dan unit usaha Anda';
            }
            $error_msg .= ' belum tersedia. Silakan tambahkan saldo awal terlebih dahulu.';
            return JSONResponseDefault('FAILED', $error_msg);
        }

        $this->db->trans_begin();

        $insert = array();
        $insert['transactioncode'] = getPost('transactioncode');
        $insert['transactiondate'] = getPost('transactiondate');
        $insert['transactionnote'] = getPost('transactionnote');

        // Clean and validate amount - remove any formatting
        $amount = getPost('amount');
        $amount = preg_replace('/[^\d]/', '', $amount); // Remove all non-numeric characters
        $insert['amount'] = (int)$amount; // Convert to integer

        $insert['transactiontype'] = getPost('transactiontype');
        $insert['accountid'] = getPost('accountid');
        $insert['workunitid'] = getPost('workunitid');
        $insert['status'] = getPost('status');
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = isBumdesUser() ? getBumdesId() : getCurrentIdUser();

        // Add Modal/HPP field if provided and transaction type is Pendapatan
        $hpp_amount = null;
        if (getPost('transactiontype') === 'Pendapatan' && getPost('hpp_amount')) {
            $hpp_amount = getPost('hpp_amount');
            // Clean HPP amount - remove any formatting
            $hpp_amount = preg_replace('/[^\d]/', '', $hpp_amount);
            $hpp_amount = (int)$hpp_amount;
            $insert['hpp_amount'] = $hpp_amount;
        }

        $this->transactions->insert($insert);
        $income_transaction_id = $this->db->insert_id();

        // Log initial status for the main transaction
        $this->load->model('Transaction_status_history', 'status_history');
        $this->status_history->logStatusChange(
            $income_transaction_id,
            null, // No old status for new transaction
            $insert['status'],
            $insert['createdby'],
            'Transaksi dibuat'
        );

        // Auto-create expense transaction if HPP is provided
        if ($hpp_amount && $hpp_amount > 0) {
            $expense_insert = array();
            $expense_insert['transactioncode'] = 'HPP-' . $insert['transactioncode'];
            $expense_insert['transactiondate'] = $insert['transactiondate'];
            $expense_insert['transactionnote'] = 'HPP untuk transaksi: ' . $insert['transactionnote'];
            $expense_insert['amount'] = $hpp_amount;
            $expense_insert['transactiontype'] = 'Pengeluaran';
            $expense_insert['accountid'] = $insert['accountid'];
            $expense_insert['workunitid'] = $insert['workunitid'];
            $expense_insert['status'] = $insert['status'];
            $expense_insert['createddate'] = getCurrentDate();
            $expense_insert['createdby'] = $insert['createdby'];
            $expense_insert['related_transaction_id'] = $income_transaction_id;

            $this->transactions->insert($expense_insert);
            $expense_transaction_id = $this->db->insert_id();

            // Log initial status for the HPP expense transaction
            $this->status_history->logStatusChange(
                $expense_transaction_id,
                null, // No old status for new transaction
                $expense_insert['status'],
                $expense_insert['createdby'],
                'Transaksi HPP dibuat otomatis'
            );
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', 'Server sedang sibuk! Silahkan coba lagi nanti');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Filter berdasarkan role
        $where = array('id' => $id);
        if (isBumdesUser()) {
            // Pengguna BUMDes hanya bisa edit transaksi dari BUMDes mereka dan unit usaha mereka
            $where['createdby'] = getBumdesId();
            $where['workunitid'] = getWorkunitId();
        } elseif (!isSuperAdmin()) {
            // BUMDes biasa hanya bisa edit transaksi mereka sendiri
            $where['createdby'] = getCurrentIdUser();
        }

        $get = $this->transactions->get($where);

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/transaction'));
        }

        $data = array();
        $data['title'] = 'Ubah Transaksi';
        $data['content'] = 'transaction/edit';

        $data['data'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        // Filter berdasarkan role untuk validasi akses
        $where = array('id' => $id);
        if (isBumdesUser()) {
            $where['createdby'] = getBumdesId();
            $where['workunitid'] = getWorkunitId();
        } elseif (!isSuperAdmin()) {
            $where['createdby'] = getCurrentIdUser();
        }

        $get = $this->transactions->get($where);

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan atau Anda tidak memiliki akses.');
        }

        // Validasi input required
        if (empty(getPost('workunitid'))) {
            return JSONResponseDefault('FAILED', 'Unit Usaha harus dipilih.');
        }

        // Validasi akses untuk pengguna BUMDes
        if (isBumdesUser()) {
            $workunitid = getPost('workunitid');
            // Pengguna BUMDes hanya bisa edit transaksi untuk unit usahanya sendiri
            if ($workunitid != getWorkunitId()) {
                return JSONResponseDefault('FAILED', 'Anda hanya dapat mengedit transaksi untuk unit usaha Anda.');
            }
        }

        // Validasi saldo awal harus ada untuk bulan transaksi
        $transactionDate = getPost('transactiondate');

        // Tentukan createdby berdasarkan role
        $createdby = isBumdesUser() ? getBumdesId() : getCurrentIdUser();

        // Cek apakah ada saldo awal untuk tahun tersebut
        // Saldo awal disimpan per tahun, bukan per bulan
        $where_balance = array(
            'YEAR(period)' => date('Y', strtotime($transactionDate)),
            'createdby' => $createdby
        );

        // Untuk pengguna BUMDes, tambahkan filter unit usaha
        if (isBumdesUser()) {
            $where_balance['workunitid'] = getWorkunitId();
        }

        $beginningBalance = $this->beginningbalances->get($where_balance);

        if ($beginningBalance->num_rows() == 0) {
            $error_msg = 'Saldo awal untuk tahun ' . date('Y', strtotime($transactionDate));
            if (isBumdesUser()) {
                $error_msg .= ' dan unit usaha Anda';
            }
            $error_msg .= ' belum tersedia. Silakan tambahkan saldo awal terlebih dahulu.';
            return JSONResponseDefault('FAILED', $error_msg);
        }

        $this->db->trans_begin();

        // Get current transaction data for status tracking
        $current_transaction = $get->row();
        $old_status = $current_transaction->status;
        $new_status = getPost('status');

        $update = array();
        $update['transactioncode'] = getPost('transactioncode');
        $update['transactiondate'] = getPost('transactiondate');
        $update['transactionnote'] = getPost('transactionnote');

        // Clean and validate amount - remove any formatting
        $amount = getPost('amount');
        $amount = preg_replace('/[^\d]/', '', $amount); // Remove all non-numeric characters
        $update['amount'] = (int)$amount; // Convert to integer

        $update['transactiontype'] = getPost('transactiontype');
        $update['accountid'] = getPost('accountid');
        $update['workunitid'] = getPost('workunitid');
        $update['status'] = $new_status;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        // Add Modal/HPP field if provided and transaction type is Pendapatan
        if (getPost('transactiontype') === 'Pendapatan' && getPost('hpp_amount')) {
            $hpp_amount = getPost('hpp_amount');
            // Clean HPP amount - remove any formatting
            $hpp_amount = preg_replace('/[^\d]/', '', $hpp_amount);
            $hpp_amount = (int)$hpp_amount;
            $update['hpp_amount'] = $hpp_amount;
        } else {
            // Clear HPP if transaction type is not Pendapatan
            $update['hpp_amount'] = null;
        }

        $this->transactions->update(array('id' => $id), $update);

        // Log status change if status has changed
        if ($old_status !== $new_status) {
            $this->load->model('Transaction_status_history', 'status_history');
            $this->status_history->logStatusChange(
                $id,
                $old_status,
                $new_status,
                getCurrentIdUser(),
                'Status diubah melalui form edit transaksi'
            );
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', 'Server sedang sibuk! Silahkan coba lagi nanti');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $id = getPost('id');

        // Filter berdasarkan role untuk validasi akses
        $where = array('id' => $id);
        if (isBumdesUser()) {
            $where['createdby'] = getBumdesId();
            $where['workunitid'] = getWorkunitId();
        } elseif (!isSuperAdmin()) {
            $where['createdby'] = getCurrentIdUser();
        }

        $get = $this->transactions->get($where);

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan atau Anda tidak memiliki akses.');
        }

        $this->db->trans_begin();

        // Check if this transaction has related transactions (HPP)
        $related_transactions = $this->transactions->get(array('related_transaction_id' => $id));

        // Delete related transactions and their status history first
        if ($related_transactions->num_rows() > 0) {
            foreach ($related_transactions->result() as $related) {
                $this->transactions->deleteWithStatusLog(array('id' => $related->id));
            }
        }

        // Delete main transaction with status history cleanup
        $this->transactions->deleteWithStatusLog(array('id' => $id));

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal menghapus transaksi');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }
}
