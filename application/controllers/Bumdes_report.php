<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Bumdes_reports $bumdes_reports
 * @property Transactions $transactions
 * @property Beginningbalances $beginningbalances
 * @property CI_Form_validation $form_validation
 * @property CI_DB_mysqli_driver $db
 */
class Bumdes_report extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Bumdes_reports', 'bumdes_reports');
        $this->load->model('Transactions', 'transactions');
        $this->load->model('Beginningbalances', 'beginningbalances');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Laporan BUMDes';
        $data['content'] = 'bumdes_report/index';

        // Filter
        $year = getGet('year', date('Y'));
        $bumdes_id = getGet('bumdes_id', '');

        $where = array();
        if ($year) {
            $where['YEAR(a.report_period)'] = $year;
        }
        if ($bumdes_id) {
            $where['a.createdby'] = $bumdes_id;
        }

        // Prepare filter parameters for statistics
        $filter_params = array();
        if ($year) {
            $filter_params['year'] = $year;
        }
        if ($bumdes_id) {
            $filter_params['bumdes_id'] = $bumdes_id;
        }

        if (isSuperAdmin()) {
            // Super Admin melihat semua laporan BUMDes
            $data['reports'] = $this->bumdes_reports->getAllReportsForSuperAdmin($where)->result();
            $data['statistics'] = $this->bumdes_reports->getSuperAdminStatistics($filter_params);

            // Load model untuk data BUMDes
            $this->load->model('Superadmins', 'superadmins');
            $data['bumdes_list'] = $this->superadmins->select('id, name, businessname')
                ->result(array('role' => 'BUMDes'));
        } else {
            // BUMDes hanya melihat laporan sendiri
            $where['a.createdby'] = getCurrentIdUser();
            $data['reports'] = $this->bumdes_reports->getReportsWithUser($where)->result();
            $data['statistics'] = $this->bumdes_reports->getReportStatistics(getCurrentIdUser(), $filter_params);
            $data['bumdes_list'] = array(); // BUMDes tidak perlu filter
        }

        $data['year'] = $year;
        $data['bumdes_id'] = $bumdes_id;
        $data['years'] = range(date('Y') - 2, date('Y') + 1); // 3 tahun ke belakang + tahun ini + 1 tahun ke depan

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (isSuperAdmin()) {
            // Super Admin tidak bisa membuat laporan
            return redirect(base_url('bumdes_report'));
        }

        $data = array();
        $data['title'] = 'Tambah Laporan BUMDes';
        $data['content'] = 'bumdes_report/add';

        // Default periode ke tahun ini
        $defaultPeriod = date('Y');
        $data['default_period'] = $defaultPeriod;

        return $this->load->view('master', $data);
    }

    public function create()
    {
        if (!isLogin() || isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        // Validasi input
        $this->form_validation->set_rules('report_period', 'Periode Laporan', 'required');
        $this->form_validation->set_rules('modal_type', 'Tipe Modal', 'required|in_list[saldo_awal,manual]');
        $this->form_validation->set_rules('laba_type', 'Tipe Laba', 'required|in_list[calculated,manual]');

        if (getPost('modal_type') === 'manual') {
            $this->form_validation->set_rules('modal_manual', 'Modal Manual', 'required|numeric|greater_than_equal_to[0]');
        }

        if (getPost('laba_type') === 'manual') {
            $this->form_validation->set_rules('laba_manual', 'Laba Manual', 'required|numeric');
        }

        if ($this->form_validation->run() === FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $reportPeriod = getPost('report_period') . '-01-01'; // Format ke YYYY-01-01 (1 Januari tahun tersebut)

        // Cek apakah laporan untuk periode ini sudah ada
        if ($this->bumdes_reports->isReportExists($reportPeriod)) {
            return JSONResponseDefault('FAILED', 'Laporan untuk periode ini sudah ada');
        }

        $this->db->trans_begin();

        try {
            $modalType = getPost('modal_type');
            $labaType = getPost('laba_type');
            $modalManual = getPost('modal_manual');
            $labaManual = getPost('laba_manual');

            // Hitung nilai-nilai
            $modalAmount = $this->bumdes_reports->calculateModal($modalType, $reportPeriod, $modalManual);
            $omsetAmount = $this->bumdes_reports->calculateOmset($reportPeriod);
            $totalPendapatan = $omsetAmount;
            $totalPengeluaran = $this->bumdes_reports->calculatePengeluaran($reportPeriod);
            $labaAmount = $this->bumdes_reports->calculateLaba($labaType, $reportPeriod, $labaManual);
            $balanceCheck = $this->bumdes_reports->calculateBalanceCheck($modalAmount, $labaAmount);

            $insert = array();
            $insert['report_code'] = $this->bumdes_reports->generateReportCode($reportPeriod);
            $insert['report_period'] = $reportPeriod;
            $insert['modal_type'] = $modalType;
            $insert['modal_amount'] = $modalAmount;
            $insert['modal_manual'] = $modalType === 'manual' ? $modalManual : null;
            $insert['omset_amount'] = $omsetAmount;
            $insert['laba_type'] = $labaType;
            $insert['laba_amount'] = $labaAmount;
            $insert['laba_manual'] = $labaType === 'manual' ? $labaManual : null;
            $insert['total_pendapatan'] = $totalPendapatan;
            $insert['total_pengeluaran'] = $totalPengeluaran;
            $insert['balance_check'] = $balanceCheck;
            $insert['notes'] = getPost('notes');
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->bumdes_reports->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Laporan berhasil dibuat');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array('id' => $id);
        if (!isSuperAdmin()) {
            $where['createdby'] = getCurrentIdUser();
        }

        $get = $this->bumdes_reports->get($where);

        if ($get->num_rows() == 0) {
            return redirect(base_url('bumdes_report'));
        }

        $report = $get->row();

        $data = array();
        $data['title'] = 'Edit Laporan BUMDes';
        $data['content'] = 'bumdes_report/edit';
        $data['data'] = $report;

        return $this->load->view('master', $data);
    }

    public function update($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $where = array('id' => $id);
        if (!isSuperAdmin()) {
            $where['createdby'] = getCurrentIdUser();
        }

        $get = $this->bumdes_reports->get($where);

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $report = $get->row();

        // Validasi input
        $this->form_validation->set_rules('modal_type', 'Tipe Modal', 'required|in_list[saldo_awal,manual]');
        $this->form_validation->set_rules('laba_type', 'Tipe Laba', 'required|in_list[calculated,manual]');

        if (getPost('modal_type') === 'manual') {
            $this->form_validation->set_rules('modal_manual', 'Modal Manual', 'required|numeric|greater_than_equal_to[0]');
        }

        if (getPost('laba_type') === 'manual') {
            $this->form_validation->set_rules('laba_manual', 'Laba Manual', 'required|numeric');
        }

        if ($this->form_validation->run() === FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $this->db->trans_begin();

        try {
            $modalType = getPost('modal_type');
            $labaType = getPost('laba_type');
            $modalManual = getPost('modal_manual');
            $labaManual = getPost('laba_manual');

            // Hitung ulang nilai-nilai
            $modalAmount = $this->bumdes_reports->calculateModal($modalType, $report->report_period, $modalManual);
            $omsetAmount = $this->bumdes_reports->calculateOmset($report->report_period);
            $totalPendapatan = $omsetAmount;
            $totalPengeluaran = $this->bumdes_reports->calculatePengeluaran($report->report_period);
            $labaAmount = $this->bumdes_reports->calculateLaba($labaType, $report->report_period, $labaManual);
            $balanceCheck = $this->bumdes_reports->calculateBalanceCheck($modalAmount, $labaAmount);

            $update = array();
            $update['modal_type'] = $modalType;
            $update['modal_amount'] = $modalAmount;
            $update['modal_manual'] = $modalType === 'manual' ? $modalManual : null;
            $update['omset_amount'] = $omsetAmount;
            $update['laba_type'] = $labaType;
            $update['laba_amount'] = $labaAmount;
            $update['laba_manual'] = $labaType === 'manual' ? $labaManual : null;
            $update['total_pendapatan'] = $totalPendapatan;
            $update['total_pengeluaran'] = $totalPengeluaran;
            $update['balance_check'] = $balanceCheck;
            $update['notes'] = getPost('notes');
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->bumdes_reports->update($update, array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengupdate data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Laporan berhasil diupdate');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    public function submit($id)
    {
        if (!isLogin() || isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $get = $this->bumdes_reports->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $report = $get->row();

        if ($report->status !== 'Draft') {
            return JSONResponseDefault('FAILED', 'Laporan sudah disubmit sebelumnya');
        }

        $this->db->trans_begin();

        $update = array();
        $update['status'] = 'Submitted';
        $update['submitted_date'] = getCurrentDate();
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->bumdes_reports->update($update, array('id' => $id));

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal submit laporan');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Laporan berhasil disubmit');
    }

    public function approve($id)
    {
        if (!isLogin() || !isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $get = $this->bumdes_reports->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $report = $get->row();

        if ($report->status !== 'Submitted') {
            return JSONResponseDefault('FAILED', 'Laporan belum disubmit atau sudah diproses');
        }

        $this->db->trans_begin();

        $update = array();
        $update['status'] = 'Approved';
        $update['approved_date'] = getCurrentDate();
        $update['approved_by'] = getCurrentIdUser();
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->bumdes_reports->update($update, array('id' => $id));

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal approve laporan');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Laporan berhasil diapprove');
    }

    public function reject($id)
    {
        if (!isLogin() || !isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $get = $this->bumdes_reports->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $report = $get->row();

        if ($report->status !== 'Submitted') {
            return JSONResponseDefault('FAILED', 'Laporan belum disubmit atau sudah diproses');
        }

        $this->db->trans_begin();

        $update = array();
        $update['status'] = 'Rejected';
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->bumdes_reports->update($update, array('id' => $id));

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal reject laporan');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Laporan berhasil direject');
    }

    public function delete($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $where = array('id' => $id);
        if (!isSuperAdmin()) {
            $where['createdby'] = getCurrentIdUser();
        }

        $get = $this->bumdes_reports->get($where);

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->db->trans_begin();

        $this->bumdes_reports->delete(array('id' => $id));

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Gagal menghapus laporan');
        }

        $this->db->trans_commit();

        return JSONResponseDefault('OK', 'Laporan berhasil dihapus');
    }

    public function calculate_preview()
    {
        if (!isLogin() || isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $period = getPost('period');
        $modalType = getPost('modal_type');
        $modalManual = getPost('modal_manual');
        $labaType = getPost('laba_type');
        $labaManual = getPost('laba_manual');

        // Debug log
        log_message('debug', 'Calculate Preview - Period: ' . $period . ', Modal Type: ' . $modalType . ', Laba Type: ' . $labaType);

        if (!$period) {
            return JSONResponseDefault('FAILED', 'Periode harus diisi');
        }

        if (!$modalType) {
            return JSONResponseDefault('FAILED', 'Tipe modal harus dipilih');
        }

        if (!$labaType) {
            return JSONResponseDefault('FAILED', 'Tipe laba harus dipilih');
        }

        $reportPeriod = $period . '-01-01'; // Format ke YYYY-01-01

        try {
            // Validasi manual input jika diperlukan
            if ($modalType === 'manual' && (!$modalManual || !is_numeric($modalManual) || $modalManual < 0)) {
                return JSONResponseDefault('FAILED', 'Modal manual harus diisi dengan nilai yang valid');
            }

            if ($labaType === 'manual' && (!$labaManual || !is_numeric($labaManual))) {
                return JSONResponseDefault('FAILED', 'Laba manual harus diisi dengan nilai yang valid');
            }

            // Hitung nilai-nilai
            $modalAmount = $this->bumdes_reports->calculateModal($modalType, $reportPeriod, $modalManual);
            $omsetAmount = $this->bumdes_reports->calculateOmset($reportPeriod);
            $totalPendapatan = $omsetAmount;
            $totalPengeluaran = $this->bumdes_reports->calculatePengeluaran($reportPeriod);
            $labaAmount = $this->bumdes_reports->calculateLaba($labaType, $reportPeriod, $labaManual);
            $balanceCheck = $this->bumdes_reports->calculateBalanceCheck($modalAmount, $labaAmount);

            // Hitung informasi tambahan untuk HPP
            $totalHpp = $this->bumdes_reports->calculateTotalHpp($reportPeriod);
            $keuntunganWithHpp = $this->bumdes_reports->calculateKeuntunganWithHpp($reportPeriod);
            $omsetTanpaHpp = $this->bumdes_reports->calculateOmsetTanpaHpp($reportPeriod);

            // Pastikan semua nilai adalah numerik
            $modalAmount = floatval($modalAmount ?? 0);
            $omsetAmount = floatval($omsetAmount ?? 0);
            $totalPendapatan = floatval($totalPendapatan ?? 0);
            $totalPengeluaran = floatval($totalPengeluaran ?? 0);
            $labaAmount = floatval($labaAmount ?? 0);
            $balanceCheck = floatval($balanceCheck ?? 0);
            $totalHpp = floatval($totalHpp ?? 0);
            $keuntunganWithHpp = floatval($keuntunganWithHpp ?? 0);
            $omsetTanpaHpp = floatval($omsetTanpaHpp ?? 0);

            $data = array(
                'modal_amount' => $modalAmount,
                'omset_amount' => $omsetAmount,
                'total_pendapatan' => $totalPendapatan,
                'total_pengeluaran' => $totalPengeluaran,
                'laba_amount' => $labaAmount,
                'balance_check' => $balanceCheck,
                'total_hpp' => $totalHpp,
                'keuntungan_with_hpp' => $keuntunganWithHpp,
                'omset_tanpa_hpp' => $omsetTanpaHpp,
                'has_hpp' => $totalHpp > 0
            );

            log_message('debug', 'Calculate Preview Success - Data: ' . json_encode($data));
            return JSONResponse(array(
                'RESULT' => 'OK',
                'MESSAGE' => 'Kalkulasi berhasil',
                'DATA' => $data
            ));
        } catch (Exception $e) {
            log_message('error', 'Calculate Preview Error: ' . $e->getMessage());
            return JSONResponseDefault('FAILED', 'Terjadi kesalahan saat menghitung: ' . $e->getMessage());
        }
    }

    public function view($id)
    {
        if (!isLogin()) {
            redirect('auth');
        }

        try {
            $result = $this->bumdes_reports->get(array('id' => $id));
            $data = $result->row();

            if (!$data) {
                echo '<div class="alert alert-danger">Laporan tidak ditemukan</div>';
                return;
            }

            // Check access permission
            if (!isSuperAdmin() && $data->createdby != getCurrentIdUser()) {
                echo '<div class="alert alert-danger">Anda tidak memiliki akses untuk melihat laporan ini</div>';
                return;
            }

            $this->load->view('bumdes_report/view_modal', compact('data'));
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Terjadi kesalahan: ' . $e->getMessage() . '</div>';
        }
    }
}
