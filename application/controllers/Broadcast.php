<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Customers $customers
 * @property Tb_Broadcast $tb_broadcast
 * @property BroadcastDetail $broadcast_detail
 * @property CI_Form_validation $form_validation
 * @property CI_DB_mysqli_driver $db
 */
// Broadcast WhatsApp feature temporarily disabled
class Broadcast_Disabled extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Customers', 'customers');
        $this->load->model('Tb_Broadcast', 'tb_broadcast');
        $this->load->model('BroadcastDetail', 'broadcast_detail');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Broadcast WhatsApp';
        $data['content'] = 'broadcast/index';
        $data['customers'] = $this->customers->order_by('name', 'ASC')->result(array(
            'a.createdby' => getCurrentIdUser(),
        ));
        $data['broadcasts'] = $this->tb_broadcast->select('a.*, b.total_all, COALESCE(c.total_processed, 0) as total_processed, COALESCE(d.total_success, 0) as total_success')
            ->join("(select broadcastid, count(*) as total_all from broadcastdetails group by broadcastid) b", 'a.id = b.broadcastid')
            ->join("(select broadcastid, count(*) as total_processed from broadcastdetails where status != 'Menunggu' group by broadcastid) c", 'a.id = c.broadcastid', 'LEFT')
            ->join("(select broadcastid, count(*) as total_success from broadcastdetails where status = 'Berhasil' group by broadcastid) d", 'a.id = d.broadcastid', 'LEFT')
            ->order_by('createddate', 'DESC')->result(array(
                'a.createdby' => getCurrentIdUser(),
            ));

        return $this->load->view('master', $data);
    }

    public function process()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $target = getPost('target', []);
        $messages = getPost('messages');

        $this->form_validation->set_rules('target[]', 'Tujuan', array('required'));
        $this->form_validation->set_rules('messages', 'Pesan', array('required'));

        if ($this->form_validation->run() === false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $insert = array();
        $insert['broadcastcode'] = 'BR-' . strtoupper(generateRandomString(10));
        $insert['messages'] = $messages;
        $insert['createddate'] = getCurrentDate();

        $this->tb_broadcast->insert($insert);
        $broadcastid = $this->db->insert_id();

        foreach ($target as $customerid) {
            $customer = $this->customers->get(array('id' => $customerid))->row();
            if ($customer == null) continue;

            $insert = array();
            $insert['broadcastid'] = $broadcastid;
            $insert['customerid'] = $customerid;
            $insert['phonenumber'] = $customer->phonenumber;
            $insert['status'] = 'Menunggu';
            $insert['createddate'] = getCurrentDate();

            $this->broadcast_detail->insert($insert);
        }

        return JSONResponseDefault('OK', 'Broadcast berhasil disimpan');
    }
}
