<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Dashboard/default';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['dashboard'] = 'Dashboard/index';

$route['auth/login'] = 'Auth/login';
$route['auth/login/process'] = 'Auth/process_login';
$route['auth/logout'] = 'Auth/logout';

$route['master/transaction/add'] = 'Transaction/add';
$route['master/transaction/add/process'] = 'Transaction/process_add';
$route['master/transaction/edit/(:num)'] = 'Transaction/edit/$1';
$route['master/transaction/edit/(:num)/process'] = 'Transaction/process_edit/$1';
$route['master/transaction/delete'] = 'Transaction/delete';

$route['master/bumdes'] = 'Master/Bumdes/index';
$route['master/bumdes/add'] = 'Master/Bumdes/add';
$route['master/bumdes/add/process'] = 'Master/Bumdes/process_add';
$route['master/bumdes/edit/(:num)'] = 'Master/Bumdes/edit/$1';
$route['master/bumdes/edit/(:num)/process'] = 'Master/Bumdes/process_edit/$1';
$route['master/bumdes/delete'] = 'Master/Bumdes/process_delete';

$route['master/select/cities'] = 'Master/Select/cities';
$route['master/select/districts'] = 'Master/Select/districts';
$route['master/select/villages'] = 'Master/Select/villages';

$route['balance/add'] = 'BalanceManager/add_balance';
$route['balance/reduce'] = 'BalanceManager/reduce_balance';
$route['balance/history/(:num)'] = 'BalanceManager/get_balance_history/$1';

$route['master/(:any)'] = 'CRUDCore/index/$1';
$route['master/(:any)/add'] = 'CRUDCore/add/$1';
$route['master/(:any)/add/process'] = 'CRUDCore/process_add/$1';
$route['master/(:any)/edit/(:num)'] = 'CRUDCore/edit/$1/$2';
$route['master/(:any)/edit/(:num)/process'] = 'CRUDCore/process_edit/$1/$2';
$route['master/(:any)/delete'] = 'CRUDCore/process_delete/$1';

// Broadcast WhatsApp feature temporarily disabled
// $route['broadcast'] = 'Broadcast/index';
// $route['broadcast/process'] = 'Broadcast/process';

// Profile routes
$route['profile'] = 'Profile/index';
$route['profile/change_password'] = 'Profile/change_password';
$route['profile/process_change_password'] = 'Profile/process_change_password';
$route['profile/update_profile'] = 'Profile/update_profile';

$route['report/transaction/daily'] = 'Report/daily';
$route['report/transaction/monthly'] = 'Report/monthly';

$route['report/balance'] = 'Report/balance';
