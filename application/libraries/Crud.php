<?php
defined('BASEPATH') or die('No direct script access allowed!');

class CRUD
{
    private $instance;
    private $feature;

    public function __construct()
    {
        $this->instance = &get_instance();
    }

    public function setFeature($feature)
    {
        $this->feature = $feature;
    }

    public function showContents($feature, $filename)
    {
        $this->feature = $feature;

        return "crudcore/$filename";
    }

    public function getElements($data = array())
    {
        // Validate feature is set
        if (empty($this->feature)) {
            return array();
        }

        // Handle special model naming for beginningbalance
        if ($this->feature == 'beginningbalance') {
            $models = 'beginningbalances';
            // Load model if not already loaded
            if (!isset($this->instance->$models)) {
                $this->instance->load->model('Beginningbalances', 'beginningbalances');
            }
        } else {
            $models = $this->feature . 's';
            // Load model if not already loaded
            if (!isset($this->instance->$models)) {
                $this->instance->load->model(ucfirst($this->feature) . 's', strtolower($this->feature) . 's');
            }
        }
        $outputs = array();

        if (isset($data['database'])) {
            if (isset($data['database']['method'])) {
                if (isset($data['database']['select'])) {
                    $this->instance->$models->select($data['database']['select']);
                }

                if (isset($data['database']['join'])) {
                    foreach ($data['database']['join'] as $key => $value) {
                        if (is_array($value)) {
                            // Handle join with type: ['condition', 'type']
                            $condition = $value[0];
                            $type = isset($value[1]) ? $value[1] : '';
                            $this->instance->$models->join($key, $condition, $type);
                        } else {
                            // Handle simple join: 'condition'
                            $this->instance->$models->join($key, $value);
                        }
                    }
                }

                if (isset($data['database']['where'])) {
                    $this->instance->$models->where($data['database']['where']);
                }

                if (isset($data['database']['order_by'])) {
                    foreach ($data['database']['order_by'] as $key => $value) {
                        $this->instance->$models->order_by($key, $value);
                    }
                }

                $outputs[$data['database']['method']] = $this->instance->$models->{$data['database']['method']}();
            }
        }

        if (isset($data['title'])) {
            $outputs['title'] = $data['title'];
        }

        if (isset($data['table'])) {
            $outputs['table'] = $data['table'];
        }

        if (isset($data['filters'])) {
            $outputs['filters'] = $data['filters'];
        }

        if (isset($data['success-redirect'])) {
            $outputs['success-redirect'] = $data['success-redirect'];
        }

        if (isset($data['row_data'])) {
            $outputs['row_data'] = $data['row_data'];
        } else {
            $outputs['row_data'] = null;
        }

        if (isset($data['fields'])) {
            $outputs['fields'] = $data['fields'];
        }

        if (isset($data['buttons'])) {
            $outputs['buttons'] = $data['buttons'];
        }

        if (isset($data['footer_button'])) {
            $outputs['footer_button'] = $data['footer_button'];
        }

        return $outputs;
    }
}
