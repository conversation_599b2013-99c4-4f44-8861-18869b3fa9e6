<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Security Helper Functions
 * Fungsi-fungsi untuk meningkatkan keamanan aplikasi
 */

if (!function_exists('checkLoginAttempts')) {
    /**
     * <PERSON>k jumlah percobaan login dalam periode tertentu
     * 
     * @param string $username
     * @param int $minutes Periode dalam menit (default: 15)
     * @param int $max_attempts Maksimal percobaan (default: 5)
     * @return bool
     */
    function checkLoginAttempts($username, $minutes = 15, $max_attempts = 5)
    {
        $CI = &get_instance();
        
        if (!$CI->db->table_exists('login_attempts')) {
            return true; // Jika tabel tidak ada, izinkan login
        }
        
        $time_limit = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        $attempts = $CI->db->where('username', $username)
                          ->where('success', 0)
                          ->where('created_at >', $time_limit)
                          ->count_all_results('login_attempts');
        
        return $attempts < $max_attempts;
    }
}

if (!function_exists('logLoginAttempt')) {
    /**
     * Log percobaan login
     * 
     * @param string $username
     * @param bool $success
     * @param string $additional_info
     */
    function logLoginAttempt($username, $success = false, $additional_info = null)
    {
        $CI = &get_instance();
        
        if (!$CI->db->table_exists('login_attempts')) {
            return false;
        }
        
        $data = [
            'username' => $username,
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent(),
            'success' => $success ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($additional_info) {
            $data['additional_info'] = $additional_info;
        }
        
        return $CI->db->insert('login_attempts', $data);
    }
}

if (!function_exists('logSecurityEvent')) {
    /**
     * Log security event
     * 
     * @param string $event
     * @param array $details
     * @param string $severity LOW|MEDIUM|HIGH|CRITICAL
     * @param int $user_id
     */
    function logSecurityEvent($event, $details = [], $severity = 'MEDIUM', $user_id = null)
    {
        $CI = &get_instance();
        
        if (!$CI->db->table_exists('security_logs')) {
            return false;
        }
        
        if (!$user_id && function_exists('getCurrentIdUser')) {
            $user_id = getCurrentIdUser();
        }
        
        $log_data = [
            'event' => $event,
            'user_id' => $user_id,
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent(),
            'details' => json_encode($details),
            'severity' => $severity,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // Log ke database
        $result = $CI->db->insert('security_logs', $log_data);
        
        // Log ke file juga untuk backup
        $log_message = "Security Event: {$event} | User: {$user_id} | IP: " . $CI->input->ip_address() . " | Details: " . json_encode($details);
        log_message('info', $log_message);
        
        return $result;
    }
}

if (!function_exists('validatePasswordStrength')) {
    /**
     * Validasi kekuatan password
     * 
     * @param string $password
     * @return array
     */
    function validatePasswordStrength($password)
    {
        $errors = [];
        $score = 0;
        
        // Minimum length
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $errors[] = 'Password minimal 8 karakter';
        }
        
        // Uppercase letter
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $errors[] = 'Password harus mengandung huruf besar';
        }
        
        // Lowercase letter
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $errors[] = 'Password harus mengandung huruf kecil';
        }
        
        // Number
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $errors[] = 'Password harus mengandung angka';
        }
        
        // Special character
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 1;
        } else {
            $errors[] = 'Password harus mengandung karakter khusus';
        }
        
        // Determine strength
        $strength = 'Sangat Lemah';
        if ($score >= 2) $strength = 'Lemah';
        if ($score >= 3) $strength = 'Sedang';
        if ($score >= 4) $strength = 'Kuat';
        if ($score >= 5) $strength = 'Sangat Kuat';
        
        return [
            'score' => $score,
            'strength' => $strength,
            'errors' => $errors,
            'is_valid' => empty($errors)
        ];
    }
}

if (!function_exists('sanitizeInput')) {
    /**
     * Sanitize input untuk mencegah XSS
     * 
     * @param mixed $input
     * @return mixed
     */
    function sanitizeInput($input)
    {
        if (is_array($input)) {
            return array_map('sanitizeInput', $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('isValidIP')) {
    /**
     * Validasi IP address
     * 
     * @param string $ip
     * @return bool
     */
    function isValidIP($ip)
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
}

if (!function_exists('checkCSRFToken')) {
    /**
     * Validasi CSRF token
     * 
     * @return bool
     */
    function checkCSRFToken()
    {
        $CI = &get_instance();
        
        if (!$CI->config->item('csrf_protection')) {
            return true; // CSRF protection disabled
        }
        
        $token_name = $CI->config->item('csrf_token_name');
        $cookie_name = $CI->config->item('csrf_cookie_name');
        
        $post_token = $CI->input->post($token_name);
        $cookie_token = $CI->input->cookie($cookie_name);
        
        return $post_token && $cookie_token && hash_equals($cookie_token, $post_token);
    }
}

if (!function_exists('generateSecureToken')) {
    /**
     * Generate secure random token
     * 
     * @param int $length
     * @return string
     */
    function generateSecureToken($length = 32)
    {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        } else {
            // Fallback (less secure)
            return substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', $length)), 0, $length);
        }
    }
}

if (!function_exists('isSecureConnection')) {
    /**
     * Cek apakah koneksi menggunakan HTTPS
     * 
     * @return bool
     */
    function isSecureConnection()
    {
        return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               $_SERVER['SERVER_PORT'] == 443 ||
               (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
    }
}

if (!function_exists('cleanupOldLogs')) {
    /**
     * Cleanup log lama untuk maintenance
     * 
     * @param int $days_to_keep
     */
    function cleanupOldLogs($days_to_keep = 30)
    {
        $CI = &get_instance();
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days_to_keep} days"));
        
        // Cleanup login attempts
        if ($CI->db->table_exists('login_attempts')) {
            $deleted_attempts = $CI->db->where('created_at <', $cutoff_date)
                                      ->delete('login_attempts');
            
            if ($deleted_attempts) {
                logSecurityEvent('LOG_CLEANUP', [
                    'table' => 'login_attempts',
                    'deleted_records' => $CI->db->affected_rows(),
                    'cutoff_date' => $cutoff_date
                ], 'LOW');
            }
        }
        
        // Cleanup security logs (keep longer - 90 days)
        $security_cutoff = date('Y-m-d H:i:s', strtotime('-90 days'));
        if ($CI->db->table_exists('security_logs')) {
            $deleted_security = $CI->db->where('created_at <', $security_cutoff)
                                       ->where('severity !=', 'CRITICAL') // Keep critical logs longer
                                       ->delete('security_logs');
        }
        
        // Cleanup old sessions
        if ($CI->db->table_exists('user_sessions')) {
            $session_cutoff = date('Y-m-d H:i:s', strtotime('-1 day'));
            $CI->db->where('last_activity <', $session_cutoff)
                   ->delete('user_sessions');
        }
    }
}

if (!function_exists('getSecurityStats')) {
    /**
     * Get security statistics
     * 
     * @return array
     */
    function getSecurityStats()
    {
        $CI = &get_instance();
        $stats = [];
        
        // Login attempts stats
        if ($CI->db->table_exists('login_attempts')) {
            $stats['login_attempts_today'] = $CI->db->where('DATE(created_at)', date('Y-m-d'))
                                                   ->count_all_results('login_attempts');
            
            $stats['failed_logins_today'] = $CI->db->where('DATE(created_at)', date('Y-m-d'))
                                                  ->where('success', 0)
                                                  ->count_all_results('login_attempts');
        }
        
        // Security events stats
        if ($CI->db->table_exists('security_logs')) {
            $stats['security_events_today'] = $CI->db->where('DATE(created_at)', date('Y-m-d'))
                                                    ->count_all_results('security_logs');
            
            $stats['critical_events_week'] = $CI->db->where('created_at >', date('Y-m-d H:i:s', strtotime('-7 days')))
                                                   ->where('severity', 'CRITICAL')
                                                   ->count_all_results('security_logs');
        }
        
        return $stats;
    }
}
