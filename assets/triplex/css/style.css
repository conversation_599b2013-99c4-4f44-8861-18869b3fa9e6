body {
    background-image: url('../img/noise-texture.svg');
    background-attachment: fixed;
    font-family: 'Inter', sans-serif;
}

a {
    text-decoration: none;
    color: #000000;
}

.btn {
    padding: 12px 24px;
    border-radius: 48px;
}

.btn.btn-primary {
    background-color: #200846;
    border-color: #200846;
}

.form-control {
    background-color: #F2F2F2;
    border: none;
    padding: 12px 18px;
    border-radius: 48px;
}

.input-group .input-group-text {
    background-color: #F2F2F2;
    border: none;
    padding: 12px 12px 12px 24px;
    border-top-left-radius: 48px;
    border-bottom-left-radius: 48px;
}

.input-group .form-control {
    border-top-right-radius: 48px;
    border-bottom-right-radius: 48px;
    padding: 12px 18px 12px 12px;
}

.login-page {
    /* display: flex; */
    padding: 50px 0;
    min-height: 100vh;
}

/* 
.login-page>div {
    width: 50%;
    margin: auto;
    padding: 0 20px;
} */

.login-page h1 {
    font-size: 48px;
    color: #200846;
    text-transform: uppercase;
    font-weight: bold;
    margin-bottom: 0;
}

.login-page h2 {
    font-size: 24px;
    margin-bottom: 0;
}

.login-page p {
    color: #666;
}

.login-page .input-group i {
    color: #000000;
}

.login-page .slick-dots {
    position: absolute;
    bottom: 1rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
}

.login-page .slick-dots li {
    width: 10px;
    height: 10px;
    background-color: rgba(217, 217, 217, 0.5);
    margin: 0 6px;
    border-radius: 50%;
    cursor: pointer;
}

.login-page .slick-dots li.slick-active {
    background-color: #D9D9D9;
}

.login-page .slick-dots li button {
    display: none;
}