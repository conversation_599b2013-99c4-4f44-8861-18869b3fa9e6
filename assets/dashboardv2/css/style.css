@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

body {
    background-color: #f6f6f6;
    font-family: 'Manrope';
}

a {
    color: #1D60BA;
    font-size: 14px;
}

a i {
    font-size: 14px;
    margin-left: 5px;
}

#main-wrapper {
    width: calc(100% / 1.25);
    transition: all 0.5s 0s ease-out;
    overflow-x: hidden;
    margin-left: calc(20%);
}

#main-wrapper.full-width {
    width: 100%;
    margin-left: unset;
}

#main-wrapper .navbar {
    padding-top: 1.25rem;
    padding-left: 1rem;
    padding-right: 1rem;
    position: relative;
}

#main-wrapper .navbar .input-group-text {
    background-color: #e2e2e2;
}

#main-wrapper .navbar .input-group.mobile {
    left: 3rem;
}

#main-wrapper .navbar input[type=search] {
    background-color: #E2E2E2;
    width: 25vw;
    border-left: unset;
}

#sidebar-wrapper {
    width: calc(100% / 5);
    min-height: 100vh;
    background-color: #fff;
    overflow: hidden;
    transition: width 0.5s 0s ease-out;
    position: fixed;
    z-index: 999;
}

#sidebar-wrapper.collapsed {
    width: 0;
}

#sidebar-wrapper .sidebar-brand {
    text-align: center;
    margin-top: 1rem;
}

#sidebar-wrapper .sidebar-brand img {
    width: calc(100% / 1.5);
}

#sidebar-wrapper .sidebar-list {
    list-style: none;
    margin-top: 2rem;
}

#sidebar-wrapper .sidebar-list li {
    padding: .75rem;
    margin-bottom: .25rem;
}

#sidebar-wrapper .sidebar-list li a {
    color: #808191;
    text-decoration: none;
}

#sidebar-wrapper .sidebar-list li.active {
    background-color: #200846;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

#sidebar-wrapper .sidebar-list li.active a {
    color: #fff;
    text-decoration: none;
}

#sidebar-wrapper .sidebar-list li a i {
    margin-right: .5rem;
    margin-left: .5rem;
}

#main-wrapper .topbar-right {
    list-style-type: none;
}

#main-wrapper .topbar-right li {
    margin-left: 1.25rem;
}

#main-wrapper .topbar-right .dropdown-menu li {
    margin-left: unset;
}

#main-wrapper .topbar-right .dropdown-menu li a {
    font-size: 14px !important;
}

#main-wrapper .topbar-right .dropdown-toggle::after {
    display: none;
}

#main-wrapper .topbar-right .dropdown-toggle img {
    width: 35px;
    height: 35px;
}

#main-wrapper .topbar-right a {
    font-size: 1.25rem !important;
}

#main-wrapper .topbar-right a i {
    color: #808191;
}

#main-wrapper .content {
    padding: 1.25rem;
    padding-bottom: 2rem;
}

#main-wrapper .content .breadcrumb-item {
    color: #11142D;
    font-weight: 600;
}

p.card-title {
    color: #808191;
    font-size: 14px;
}

h6.card-count {
    font-weight: 700;
    font-size: 20px;
}

h6.card-title {
    font-weight: 700;
    font-size: 18px;
}

.case img {
    border-radius: 50%;
    border: 2px solid #3300FF;
}

.case .case-item {
    margin-bottom: 15px;
}

.case .case-item .case-detail {
    margin-left: 10px;
}

.case .case-item .case-detail h6 {
    color: #11142D;
    font-size: 16px;
}

.case .case-item .case-detail small {
    color: #808191;
    font-size: 12px;
}

.card {
    border-radius: 8px;
    margin-bottom: 1rem;
}

.badge {
    font-weight: 400;
    padding: 8px 16px;
}

.badge.badge-success {
    background-color: rgba(52, 199, 89, 0.2);
    color: #000000;
}

.badge.badge-warning {
    background-color: rgba(255, 149, 0, 0.2);
    color: #000000;
}

.badge.badge-success:before {
    font-family: var(--fa-style-family, "Font Awesome 6 Free");
    content: '\f111';
    color: #34C759;
    background-color: #34C759;
    border-radius: 50%;
    margin-right: 5px;
}

.badge.badge-warning:before {
    font-family: var(--fa-style-family, "Font Awesome 6 Free");
    content: '\f111';
    color: #FF9500;
    background-color: #FF9500;
    border-radius: 50%;
    margin-right: 5px;
}

.card-news {
    display: flex;
    overflow: hidden;
}

.card-news .news-item {
    width: 250px;
    margin-right: 10px;
}

.card-news .news-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center;
    border-radius: 10px;
}

.card-news .news-item .card .card-body {
    padding: .25rem;
}

.card-news .news-item .card .card-body .news-detail {
    padding: .5rem;
}

.news-detail h6 {
    color: #11142D;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 10px;
}

.news-detail p {
    font-size: 12px;
    color: #525571;
    margin-bottom: 0;
    text-align: justify;
}

.news-detail a {
    display: inline-block;
    text-decoration: none;
    color: #2AC16B;
    font-size: 14px;
    margin-top: .5rem;
    font-weight: 700;
}

.news-detail a::after {
    font-family: var(--fa-style-family, "Font Awesome 6 Free");
    content: '\f061';
    margin-left: 5px;
    font-size: 12px;
}

.slick-slide {
    height: unset !important;
}

.table {
    font-family: 'Montserrat';
}

.table th,
.table td {
    font-size: 14px;
}

.collapsed-sidebar {
    display: flex;
    justify-content: center;
}

.collapsed-sidebar a {
    display: block;
    width: 50px;
    height: 50px;
    background-color: #dee2e6;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    border-radius: 50%;
    font-size: 16px;
}

.collapsed-sidebar a i {
    margin-left: unset;
}

.sidebar-toggle {
    align-self: center;
    position: absolute;
    left: -5rem;
}

.sidebar-toggle.mobile {
    left: 1.5rem;
}

.sidebar-toggle a i {
    font-size: 22px !important;
    margin-right: 2rem;
    color: #808191;
}

@media screen and (max-width: 1199px) {
    #main-wrapper {
        margin-left: 0;
        width: 100%;
    }

    .sidebar-toggle {
        left: 1.5rem;
    }

    #main-wrapper .navbar .input-group {
        left: 3rem;
    }

    #sidebar-wrapper {
        width: calc(100% / 4);
    }
}

@media screen and (max-width: 991px) {
    #sidebar-wrapper {
        width: calc(100% / 3);
    }
}

@media screen and (max-width: 767px) {
    #sidebar-wrapper {
        width: calc(100% / 3);
    }
}

@media screen and (max-width: 575px) {
    #sidebar-wrapper {
        width: calc(100% / 1.5);
    }

    #searchbar {
        display: none;
    }

    .topbar-right {
        justify-content: space-between;
        padding-left: 0;
    }
}