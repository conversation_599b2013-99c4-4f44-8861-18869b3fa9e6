/**
 * Developed by <PERSON><PERSON> (CEO Malang Creative Team)
 * Simple AjaxRequest
 * 
 * Create at 2020 and always be update
 * https://www.malangcreativeteam.biz.id/
 */

+function ($) {
    let AjaxRequest = function (form, options) {
        this.form = form;
        this.options = options;

        if (typeof this.options.disabledElements === 'undefined') {
            this.options.disabledElements = true;
        }

        this.initialize();
    }

    AjaxRequest.prototype.initialize = function () {
        this.submitHandler();
    }

    AjaxRequest.prototype.initializeForm = function () {
        let form = this.form;
        let action = form.attr('action');
        let method = form.attr('method');

        if (typeof action === 'undefined') {
            action = '';
        }

        if (typeof method === 'undefined') {
            method = 'GET';
        }

        this.action = action;
        this.method = method;
    }

    AjaxRequest.prototype.disabledElements = function () {
        let form = this.form;
        return form.find('textarea, input, select, button').attr('disabled', true);
    }

    AjaxRequest.prototype.enabledElements = function () {
        let form = this.form;
        return form.find('textarea, input, select, button').removeAttr('disabled');
    }

    AjaxRequest.prototype.submitHandler = function () {
        let form = this.form;
        let these = this;
        this.initializeForm();

        form.submit(function (e) {
            e.preventDefault();

            these.request(this);
        });
    }

    AjaxRequest.prototype.request = function (these) {
        let that = this;
        let formData = new FormData(these);
        let AjaxOption = {};

        if (this.options.disabledElements == true) {
            this.disabledElements();
        }

        AjaxOption['url'] = this.action;
        AjaxOption['method'] = this.method;
        AjaxOption['data'] = formData;
        AjaxOption['dataType'] = 'json';

        if (typeof this.options.dataType !== 'undefined') {
            AjaxOption['dataType'] = this.options.dataType;
        }

        AjaxOption['processData'] = false;
        AjaxOption['contentType'] = false;

        if (typeof this.options.success !== 'undefined') {
            AjaxOption['success'] = function (response) {
                that.enabledElements();
                that.options.success(response);
            }
        }

        if (typeof this.options.error !== 'undefined') {
            AjaxOption['error'] = function (response) {
                that.enabledElements();
                that.options.error(response);
            }
        }

        $.ajax(AjaxOption);
    }

    $.AjaxRequest = function (form, options) {
        let elementsForm = $(form);
        new AjaxRequest(elementsForm, options);
    }

    $.AjaxRequest.Constructor = AjaxRequest;
}(jQuery);