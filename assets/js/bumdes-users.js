/**
 * BUMDes Users Management JavaScript
 * Handles DataTable initialization and user interactions
 */

// Global variable to store DataTable instance
var usersTable = null;

/**
 * Safely initialize DataTable
 */
function initializeUsersDataTable() {
    try {
        // Check if DataTable is already initialized
        if ($.fn.DataTable.isDataTable('.datatables')) {
            // Destroy existing DataTable first
            $('.datatables').DataTable().destroy();
        }

        // Initialize DataTable with custom options
        usersTable = $('.datatables').DataTable({
            "pageLength": 10,
            "searching": false, // Disable default search
            "dom": 'lrtip', // Remove default search box
            "order": [[6, 'desc']], // Sort by created date (column 6)
            "language": {
                "lengthMenu": "Tampilkan _MENU_ data per halaman",
                "zeroRecords": "Tidak ada data yang ditemukan",
                "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                "infoEmpty": "Tidak ada data tersedia",
                "infoFiltered": "(difilter dari _MAX_ total data)",
                "paginate": {
                    "first": "Pertama",
                    "last": "Terakhir",
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                }
            },
            "columnDefs": [
                { "orderable": false, "targets": [0, 7] } // Disable sorting for checkbox and action columns
            ]
        });

        // Setup search functionality
        setupSearchAndFilter();

        // Setup bulk actions
        setupBulkActions();

        console.log('DataTable initialized successfully');
        return usersTable;
    } catch (error) {
        console.error('Error initializing DataTable:', error);
        return null;
    }
}

/**
 * Setup search and filter functionality
 */
function setupSearchAndFilter() {
    // Custom search functionality
    $('#searchInput').off('keyup').on('keyup', function () {
        if (usersTable) {
            usersTable.search(this.value).draw();
        }
    });

    // Custom status filter
    $('#statusFilter').off('change').on('change', function () {
        if (usersTable) {
            var status = this.value;
            if (status === '') {
                usersTable.column(5).search('').draw(); // Column 5 is status column (adjusted for checkbox)
            } else {
                usersTable.column(5).search(status).draw();
            }
        }
    });
}

/**
 * Setup bulk actions functionality
 */
function setupBulkActions() {
    // Select all checkbox
    $('#selectAll').off('change').on('change', function () {
        $('.user-checkbox').prop('checked', this.checked);
        updateBulkActions();
    });

    // Individual checkbox change
    $(document).off('change', '.user-checkbox').on('change', '.user-checkbox', function () {
        updateBulkActions();

        // Update select all checkbox
        var totalCheckboxes = $('.user-checkbox').length;
        var checkedCheckboxes = $('.user-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });
}

/**
 * Update bulk actions visibility
 */
function updateBulkActions() {
    var checkedCount = $('.user-checkbox:checked').length;
    $('#selectedCount').text(checkedCount);

    if (checkedCount > 0) {
        $('#bulkActions').show();
    } else {
        $('#bulkActions').hide();
    }
}

/**
 * Clear all filters
 */
function clearFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('');
    if (usersTable) {
        usersTable.search('').columns().search('').draw();
    }
}

/**
 * Refresh DataTable
 */
function refreshUsersTable() {
    if (usersTable) {
        usersTable.ajax.reload(null, false); // Keep current page
    } else {
        // If table doesn't exist, reload the page
        window.location.reload();
    }
}

/**
 * Bulk toggle status
 */
function bulkToggleStatus(newStatus) {
    var selectedIds = [];
    var selectedNames = [];

    $('.user-checkbox:checked').each(function () {
        selectedIds.push($(this).val());
        selectedNames.push($(this).data('name'));
    });

    if (selectedIds.length === 0) {
        swal('Peringatan', 'Pilih pengguna terlebih dahulu.', 'warning');
        return;
    }

    var actionText = newStatus === 'Aktif' ? 'mengaktifkan' : 'menonaktifkan';
    var userList = selectedNames.join(', ');

    swal({
        title: 'Konfirmasi',
        content: {
            element: "div",
            attributes: {
                innerHTML: `Apakah Anda yakin ingin ${actionText} pengguna berikut?<br><br><strong>${userList}</strong>`
            }
        },
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Batal',
                value: false,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: 'Ya, Lanjutkan',
                value: true,
                visible: true,
                className: 'btn btn-primary',
                closeModal: true
            }
        }
    }).then((willProceed) => {
        if (willProceed) {
            // Process each selected user
            var promises = selectedIds.map(function (id) {
                return $.ajax({
                    url: base_url + 'bumdes_users/toggle_status',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        status: newStatus
                    }
                });
            });

            Promise.all(promises).then(function () {
                swal({
                    title: 'Berhasil!',
                    text: `${selectedIds.length} pengguna berhasil ${actionText}.`,
                    icon: 'success',
                    button: 'OK'
                }).then(() => {
                    window.location.reload();
                });
            }).catch(function () {
                swal({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memproses beberapa pengguna.',
                    icon: 'error',
                    button: 'OK'
                });
            });
        }
    });
}

/**
 * Bulk delete users
 */
function bulkDelete() {
    var selectedIds = [];
    var selectedNames = [];

    $('.user-checkbox:checked').each(function () {
        selectedIds.push($(this).val());
        selectedNames.push($(this).data('name'));
    });

    if (selectedIds.length === 0) {
        swal('Peringatan', 'Pilih pengguna terlebih dahulu.', 'warning');
        return;
    }

    var userList = selectedNames.join(', ');

    swal({
        title: 'Konfirmasi Hapus',
        content: {
            element: "div",
            attributes: {
                innerHTML: `Apakah Anda yakin ingin menghapus pengguna berikut?<br><br><strong>${userList}</strong><br><br>Data yang sudah dihapus tidak dapat dikembalikan.`
            }
        },
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Batal',
                value: false,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: 'Ya, Hapus',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: true
            }
        },
        dangerMode: true
    }).then((willDelete) => {
        if (willDelete) {
            // Process each selected user
            var promises = selectedIds.map(function (id) {
                return $.ajax({
                    url: base_url + 'bumdes_users/delete',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    }
                });
            });

            Promise.all(promises).then(function () {
                swal({
                    title: 'Berhasil!',
                    text: `${selectedIds.length} pengguna berhasil dihapus.`,
                    icon: 'success',
                    button: 'OK'
                }).then(() => {
                    window.location.reload();
                });
            }).catch(function () {
                swal({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menghapus beberapa pengguna.',
                    icon: 'error',
                    button: 'OK'
                });
            });
        }
    });
}

// Initialize when document is ready
$(document).ready(function () {
    // Small delay to ensure all elements are loaded
    setTimeout(function () {
        initializeUsersDataTable();
    }, 100);
});

// Re-initialize on window load as backup
window.addEventListener('load', function () {
    if (!usersTable || !$.fn.DataTable.isDataTable('.datatables')) {
        setTimeout(function () {
            initializeUsersDataTable();
        }, 200);
    }
});
