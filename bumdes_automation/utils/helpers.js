const { faker } = require('@faker-js/faker');
const moment = require('moment');

class TestHelpers {
  /**
   * Generate random test data
   */
  static generateTestData() {
    return {
      // User data
      user: {
        name: faker.person.fullName(),
        username: faker.internet.userName().toLowerCase(),
        password: 'Test123!',
        phone: faker.phone.number('08##########'),
        address: faker.location.streetAddress()
      },

      // Business data
      business: {
        ownerName: faker.person.fullName(),
        businessName: `BUMDes ${faker.company.name()}`,
        address: faker.location.streetAddress()
      },

      // Transaction data
      transaction: {
        amount: faker.number.int({ min: 100000, max: 10000000 }),
        description: faker.lorem.sentence(),
        date: moment().format('YYYY-MM-DD'),
        type: faker.helpers.arrayElement(['Pendapatan', 'Pengeluaran'])
      },

      // Report data
      report: {
        period: moment().format('YYYY'),
        month: moment().format('MM'),
        year: moment().format('YYYY')
      }
    };
  }

  /**
   * Format currency to Indonesian Rupiah
   */
  static formatCurrency(amount) {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Format date to Indonesian format
   */
  static formatDate(date, format = 'DD MMMM YYYY') {
    return moment(date).locale('id').format(format);
  }

  /**
   * Generate random string
   */
  static generateRandomString(length = 10) {
    return faker.string.alphanumeric(length);
  }

  /**
   * Generate random email
   */
  static generateRandomEmail() {
    return faker.internet.email().toLowerCase();
  }

  /**
   * Generate random phone number
   */
  static generateRandomPhone() {
    return faker.phone.number('08##########');
  }

  /**
   * Wait for element to be visible
   */
  static async waitForElement(page, selector, timeout = 30000) {
    await page.waitForSelector(selector, {
      state: 'visible',
      timeout
    });
  }

  /**
   * Wait for page to load completely
   */
  static async waitForPageLoad(page) {
    await page.waitForLoadState('networkidle');
    await page.waitForLoadState('domcontentloaded');
  }

  /**
   * Take screenshot with timestamp
   */
  static async takeScreenshot(page, name) {
    const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
    const filename = `${name}_${timestamp}.png`;
    await page.screenshot({
      path: `test-results/screenshots/${filename}`,
      fullPage: true
    });
    return filename;
  }

  /**
   * Clear and fill input field
   */
  static async clearAndFill(page, selector, value) {
    await page.fill(selector, '');
    await page.fill(selector, value);
  }

  /**
   * Select option from dropdown
   */
  static async selectOption(page, selector, value) {
    await page.selectOption(selector, value);
  }

  /**
   * Click and wait for navigation
   */
  static async clickAndWaitForNavigation(page, selector) {
    await Promise.all([
      page.waitForNavigation(),
      page.click(selector)
    ]);
  }

  /**
   * Verify text content
   */
  static async verifyTextContent(page, selector, expectedText) {
    const element = await page.locator(selector);
    const text = await element.textContent();
    if (!text || !text.includes(expectedText)) {
      throw new Error(`Expected element "${selector}" to contain text "${expectedText}", but got "${text}"`);
    }
    return true;
  }

  /**
   * Verify element is visible
   */
  static async verifyElementVisible(page, selector) {
    const element = await page.locator(selector);
    const isVisible = await element.isVisible();
    if (!isVisible) {
      throw new Error(`Expected element "${selector}" to be visible, but it was not`);
    }
    return true;
  }

  /**
   * Verify element is hidden
   */
  static async verifyElementHidden(page, selector) {
    const element = await page.locator(selector);
    const isVisible = await element.isVisible();
    if (isVisible) {
      throw new Error(`Expected element "${selector}" to be hidden, but it was visible`);
    }
    return true;
  }

  /**
   * Get table row count
   */
  static async getTableRowCount(page, tableSelector = '.datatables tbody tr') {
    return await page.locator(tableSelector).count();
  }

  /**
   * Get text from element
   */
  static async getElementText(page, selector) {
    return await page.locator(selector).textContent();
  }

  /**
   * Check if element exists
   */
  static async elementExists(page, selector) {
    try {
      await page.waitForSelector(selector, { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Scroll to element
   */
  static async scrollToElement(page, selector) {
    await page.locator(selector).scrollIntoViewIfNeeded();
  }

  /**
   * Wait for API response
   */
  static async waitForAPIResponse(page, urlPattern) {
    return await page.waitForResponse(response =>
      response.url().includes(urlPattern) && response.status() === 200
    );
  }
}

module.exports = TestHelpers;
