require('dotenv').config();

const config = {
  // Base URL
  baseURL: process.env.BASE_URL || 'http://localhost/bumdes',

  // Test Users
  users: {
    superAdmin: {
      username: process.env.SUPER_ADMIN_USERNAME || 'superadmin',
      password: process.env.SUPER_ADMIN_PASSWORD || 'password123',
      role: 'Super Admin'
    },
    bumdes: {
      username: process.env.BUMDES_USERNAME || 'bumdes_test',
      password: process.env.BUMDES_PASSWORD || 'password123',
      role: 'BUMDes'
    },
    bumdesUser: {
      username: process.env.BUMDES_USER_USERNAME || 'bumdes_user_test',
      password: process.env.BUMDES_USER_PASSWORD || 'password123',
      role: 'BUMDes User'
    }
  },

  // Test Configuration
  timeout: {
    default: parseInt(process.env.TIMEOUT) || 30000,
    long: 60000,
    short: 10000
  },

  // Test Data
  testData: {
    villageId: process.env.TEST_VILLAGE_ID || '1',
    workunitId: process.env.TEST_WORKUNIT_ID || '1',
    bumdesName: process.env.TEST_BUMDES_NAME || 'BUMDes Test Automation'
  },

  // Selectors
  selectors: {
    // Login
    loginForm: '#frmLogin',
    usernameInput: 'input[name="username"]',
    passwordInput: 'input[name="password"]',
    loginButton: 'button[type="submit"]',

    // Navigation
    sidebar: '.sidebar',
    navbar: '.navbar',
    userDropdown: '.user-dropdown',
    logoutButton: 'a[href*="logout"]',

    // Common
    submitButton: 'button[type="submit"]',
    cancelButton: '.btn-cancel',
    deleteButton: '.btn-delete',
    editButton: '.btn-edit',
    addButton: '.btn-add',

    // Tables
    dataTable: '.datatables',
    tableRows: 'tbody tr',

    // Modals
    modal: '.modal',
    modalTitle: '.modal-title',
    modalBody: '.modal-body',
    modalFooter: '.modal-footer',
    closeModal: '.btn-close',

    // Alerts
    successAlert: '.alert-success',
    errorAlert: '.alert-danger',
    warningAlert: '.alert-warning',

    // SweetAlert (v1)
    sweetAlert: '.sweet-alert',
    sweetAlertTitle: '.sweet-alert .swal-title, .sweet-alert .title',
    sweetAlertContent: '.sweet-alert .swal-text, .sweet-alert p',
    sweetAlertConfirm: '.sweet-alert .confirm',
    sweetAlertCancel: '.sweet-alert .cancel',
    sweetAlertError: '.swal-icon--error',
    sweetAlertSuccess: '.swal-icon--success',

    // Forms
    formGroup: '.form-group',
    formControl: '.form-control',
    selectControl: 'select.form-control',

    // Specific Features
    transactionForm: '#frmTransaction',
    userForm: '#frmUser',
    reportForm: '#frmReport'
  },

  // URLs
  urls: {
    login: '/auth/login',
    dashboard: '/dashboard',
    logout: '/auth/logout',

    // Master Data
    bumdes: '/master/bumdes',
    workunits: '/master/workunits',

    // User Management
    bumdesUsers: '/bumdes_users',

    // Transactions
    transactions: '/transaction',
    beginningBalance: '/beginning_balance',

    // Reports
    reports: '/reports',
    transactionHistory: '/transaction_history',

    // Registration
    register: '/register'
  }
};

module.exports = config;
