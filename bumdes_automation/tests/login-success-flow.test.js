const { test, expect } = require('@playwright/test');
const LoginPage = require('../pages/LoginPage');
const DashboardPage = require('../pages/DashboardPage');

test.describe('Login Success Flow Tests', () => {
  let loginPage;
  let dashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    await loginPage.goto();
  });

  test('should complete full login flow: credentials → success alert → click OK → dashboard @smoke', async () => {
    console.log('Testing complete login flow with SweetAlert success handling...');
    
    const startTime = Date.now();
    
    // Step 1: Fill credentials
    console.log('Step 1: Filling login credentials...');
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    
    // Step 2: Submit login
    console.log('Step 2: Submitting login form...');
    await loginPage.clickLogin();
    
    // Step 3: Wait for and detect success SweetAlert
    console.log('Step 3: Waiting for success SweetAlert...');
    await loginPage.page.waitForTimeout(3000);
    
    const successResult = await loginPage.checkForLoginSuccess();
    console.log('Success SweetAlert result:', successResult);
    
    if (successResult.hasSuccess) {
      console.log('✅ Success SweetAlert detected and OK button clicked');
      expect(successResult.buttonClicked).toBeTruthy();
    } else {
      console.log('⚠️ No success SweetAlert detected, checking for direct redirect...');
    }
    
    // Step 4: Verify redirect to dashboard
    console.log('Step 4: Verifying redirect to dashboard...');
    await loginPage.verifySuccessfulLogin();
    
    // Step 5: Verify dashboard is fully loaded
    console.log('Step 5: Verifying dashboard is fully loaded...');
    await dashboardPage.verifyDashboardLoaded();
    
    const totalTime = Date.now() - startTime;
    console.log(`✅ Complete login flow successful in ${totalTime}ms`);
    
    // Performance check
    expect(totalTime).toBeLessThan(25000); // 25 seconds max
  });

  test('should handle success SweetAlert for BUMDes user @smoke', async () => {
    console.log('Testing BUMDes user login with success SweetAlert...');
    
    // Use login method which handles SweetAlert internally
    const loginResult = await loginPage.login(
      loginPage.config.users.bumdes.username,
      loginPage.config.users.bumdes.password
    );
    
    console.log('BUMDes login result:', loginResult);
    
    if (loginResult.success) {
      console.log('✅ BUMDes login successful');
      
      // Verify we're on dashboard
      await loginPage.verifySuccessfulLogin();
      await dashboardPage.verifyDashboardLoaded();
    } else {
      console.log('❌ BUMDes login failed:', loginResult);
      throw new Error('BUMDes login should have succeeded');
    }
  });

  test('should handle success SweetAlert for BUMDes User @smoke', async () => {
    console.log('Testing BUMDes User login with success SweetAlert...');
    
    // Use login method which handles SweetAlert internally
    const loginResult = await loginPage.login(
      loginPage.config.users.bumdesUser.username,
      loginPage.config.users.bumdesUser.password
    );
    
    console.log('BUMDes User login result:', loginResult);
    
    if (loginResult.success) {
      console.log('✅ BUMDes User login successful');
      
      // Verify we're on dashboard
      await loginPage.verifySuccessfulLogin();
      await dashboardPage.verifyDashboardLoaded();
    } else {
      console.log('❌ BUMDes User login failed:', loginResult);
      throw new Error('BUMDes User login should have succeeded');
    }
  });

  test('should verify success alert content and behavior @functionality', async ({ page }) => {
    console.log('Testing success alert content and behavior...');
    
    // Fill and submit login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for success alert
    await page.waitForTimeout(3000);
    
    // Check if success icon is visible
    const successIconVisible = await page.isVisible('.swal-icon--success');
    console.log('Success icon visible:', successIconVisible);
    
    if (successIconVisible) {
      console.log('✅ Success icon detected');
      
      // Get alert content before clicking OK
      const alertTitle = await loginPage.getText(loginPage.selectors.sweetAlertTitle);
      const alertContent = await loginPage.getText(loginPage.selectors.sweetAlertContent);
      
      console.log('Success alert content:', { title: alertTitle, content: alertContent });
      
      // Verify content contains success indicators
      const successText = (alertTitle + ' ' + alertContent).toLowerCase();
      const successKeywords = ['success', 'berhasil', 'sukses', 'login', 'masuk'];
      
      const hasSuccessKeyword = successKeywords.some(keyword => successText.includes(keyword));
      if (hasSuccessKeyword) {
        console.log('✅ Success message contains appropriate success keywords');
      } else {
        console.log('⚠️ Success message may not contain clear success indication');
        console.log('Success text:', successText);
      }
      
      // Now handle the alert
      const successResult = await loginPage.checkForLoginSuccess();
      expect(successResult.hasSuccess).toBeTruthy();
      expect(successResult.buttonClicked).toBeTruthy();
    } else {
      console.log('ℹ️ No success icon found, may be different alert type or direct redirect');
    }
    
    // Verify final state
    await loginPage.verifySuccessfulLogin();
  });

  test('should test different OK button click methods @regression', async ({ page }) => {
    console.log('Testing different methods to click OK button...');
    
    // Fill and submit login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for alert
    await page.waitForTimeout(3000);
    
    // Check if success alert is present
    const alertVisible = await page.isVisible('.sweet-alert');
    const successIconVisible = await page.isVisible('.swal-icon--success');
    
    if (alertVisible && successIconVisible) {
      console.log('Success alert detected, testing button click methods...');
      
      // Method 1: Try specific OK button selector
      const okButtonSelectors = [
        '.sweet-alert .confirm',
        '.sweet-alert button',
        '.swal-button--confirm',
        'button:contains("OK")',
        'button:contains("Oke")'
      ];
      
      let buttonFound = false;
      for (const selector of okButtonSelectors) {
        const isVisible = await page.isVisible(selector);
        if (isVisible) {
          console.log(`✅ Found OK button with selector: ${selector}`);
          await page.click(selector);
          buttonFound = true;
          break;
        }
      }
      
      if (!buttonFound) {
        console.log('No OK button found, trying Enter key...');
        await page.keyboard.press('Enter');
      }
      
      // Wait for alert to disappear
      await page.waitForSelector('.sweet-alert', { state: 'hidden', timeout: 5000 });
      console.log('✅ Success alert dismissed');
    } else {
      console.log('ℹ️ No success alert to handle');
    }
    
    // Verify final state
    await loginPage.verifySuccessfulLogin();
  });

  test('should handle slow network conditions @performance', async ({ page }) => {
    console.log('Testing login flow with slow network simulation...');
    
    // Simulate slow network
    await page.route('**/*', route => {
      setTimeout(() => route.continue(), 1000); // 1 second delay
    });
    
    const startTime = Date.now();
    
    // Perform login
    const loginResult = await loginPage.login(
      loginPage.config.users.superAdmin.username,
      loginPage.config.users.superAdmin.password
    );
    
    const loginTime = Date.now() - startTime;
    console.log(`Login completed in ${loginTime}ms with network delay`);
    
    // Should still succeed despite slow network
    expect(loginResult.success).toBeTruthy();
    
    // Verify dashboard
    await loginPage.verifySuccessfulLogin();
    
    const totalTime = Date.now() - startTime;
    console.log(`Total time with slow network: ${totalTime}ms`);
    
    // Should complete within reasonable time even with delays
    expect(totalTime).toBeLessThan(45000); // 45 seconds max with delays
  });

  test('should verify no alert remains after successful login @cleanup', async ({ page }) => {
    console.log('Testing that no alerts remain after successful login...');
    
    // Perform complete login
    await loginPage.loginAsSuperAdmin();
    await loginPage.verifySuccessfulLogin();
    
    // Verify no SweetAlert elements are visible
    const alertVisible = await page.isVisible('.sweet-alert');
    const overlayVisible = await page.isVisible('.swal-overlay');
    
    expect(alertVisible).toBeFalsy();
    expect(overlayVisible).toBeFalsy();
    
    console.log('✅ No alert elements remain after login');
    
    // Verify dashboard is interactive
    const sidebarClickable = await page.isEnabled('.sidebar');
    expect(sidebarClickable).toBeTruthy();
    
    console.log('✅ Dashboard is interactive after login');
  });

  test('should test rapid successive logins @stress', async () => {
    console.log('Testing rapid successive login attempts...');
    
    const attempts = 3;
    const results = [];
    
    for (let i = 0; i < attempts; i++) {
      console.log(`Login attempt ${i + 1}/${attempts}`);
      
      // Go to login page
      await loginPage.goto();
      
      const startTime = Date.now();
      const loginResult = await loginPage.login(
        loginPage.config.users.superAdmin.username,
        loginPage.config.users.superAdmin.password
      );
      const duration = Date.now() - startTime;
      
      results.push({
        attempt: i + 1,
        success: loginResult.success,
        duration: duration
      });
      
      if (loginResult.success) {
        await loginPage.verifySuccessfulLogin();
        console.log(`✅ Attempt ${i + 1} successful in ${duration}ms`);
        
        // Logout for next attempt (if not last)
        if (i < attempts - 1) {
          await page.goto(loginPage.config.baseURL + '/logout');
          await page.waitForTimeout(1000);
        }
      } else {
        console.log(`❌ Attempt ${i + 1} failed in ${duration}ms`);
      }
    }
    
    console.log('All login attempts completed:', results);
    
    // All attempts should succeed
    const allSuccessful = results.every(r => r.success);
    expect(allSuccessful).toBeTruthy();
  });
});
