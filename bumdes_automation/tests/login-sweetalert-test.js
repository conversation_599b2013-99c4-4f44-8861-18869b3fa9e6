const { test, expect } = require('@playwright/test');
const LoginPage = require('../pages/LoginPage');
const DashboardPage = require('../pages/DashboardPage');

test.describe('Login with SweetAlert Tests', () => {
  let loginPage;
  let dashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    await loginPage.goto();
  });

  test('should handle SweetAlert during Super Admin login @smoke', async () => {
    console.log('Testing Super Admin login with SweetAlert handling...');
    
    // Perform login
    await loginPage.loginAsSuperAdmin();
    
    // Verify successful login (should handle SweetAlert automatically)
    await loginPage.verifySuccessfulLogin();
    
    // Verify dashboard is loaded
    await dashboardPage.verifyDashboardLoaded();
    
    console.log('✅ Super Admin login with Sweet<PERSON>lert completed successfully');
  });

  test('should handle <PERSON>Alert during BUMDes login @smoke', async () => {
    console.log('Testing BUMDes login with SweetAlert handling...');
    
    // Perform login
    await loginPage.loginAsBumdes();
    
    // Verify successful login (should handle SweetAlert automatically)
    await loginPage.verifySuccessfulLogin();
    
    // Verify dashboard is loaded
    await dashboardPage.verifyDashboardLoaded();
    
    console.log('✅ BUMDes login with SweetAlert completed successfully');
  });

  test('should handle SweetAlert during BUMDes User login @smoke', async () => {
    console.log('Testing BUMDes User login with SweetAlert handling...');
    
    // Perform login
    await loginPage.loginAsBumdesUser();
    
    // Verify successful login (should handle SweetAlert automatically)
    await loginPage.verifySuccessfulLogin();
    
    // Verify dashboard is loaded
    await dashboardPage.verifyDashboardLoaded();
    
    console.log('✅ BUMDes User login with SweetAlert completed successfully');
  });

  test('should handle login error with SweetAlert @regression', async () => {
    console.log('Testing login error handling...');
    
    try {
      // Try login with invalid credentials
      await loginPage.login('invalid_user', 'invalid_password');
      
      // Check if there's a SweetAlert for error
      const alertResult = await loginPage.handleSweetAlert('confirm');
      
      if (alertResult.handled) {
        console.log('✅ Error SweetAlert handled:', alertResult);
      } else {
        console.log('ℹ️ No SweetAlert appeared for invalid login');
      }
      
      // Should still be on login page
      const currentURL = await loginPage.getCurrentURL();
      expect(currentURL).toContain('login');
      
    } catch (error) {
      console.log('Login error test completed:', error.message);
    }
  });

  test('should test SweetAlert handling methods @functionality', async () => {
    console.log('Testing SweetAlert handling methods...');
    
    // Test the base SweetAlert handling method
    const result = await loginPage.handleSweetAlert('confirm');
    
    if (result.handled) {
      console.log('✅ SweetAlert was found and handled:', result);
    } else {
      console.log('ℹ️ No SweetAlert found (expected on login page):', result);
    }
    
    // This test verifies the method works without errors
    expect(result).toHaveProperty('handled');
  });

  test('should verify login flow timing with SweetAlert @performance', async () => {
    console.log('Testing login performance with SweetAlert...');
    
    const startTime = Date.now();
    
    // Perform complete login flow
    await loginPage.loginAsSuperAdmin();
    await loginPage.verifySuccessfulLogin();
    await dashboardPage.verifyDashboardLoaded();
    
    const totalTime = Date.now() - startTime;
    
    // Login with SweetAlert should complete within reasonable time
    expect(totalTime).toBeLessThan(15000); // 15 seconds
    
    console.log(`✅ Login flow completed in ${totalTime}ms`);
  });

  test('should handle multiple SweetAlerts if they appear @edge-case', async () => {
    console.log('Testing multiple SweetAlert handling...');
    
    // Perform login
    await loginPage.loginAsSuperAdmin();
    
    // Try to handle additional SweetAlerts that might appear
    let alertCount = 0;
    let maxAttempts = 3;
    
    for (let i = 0; i < maxAttempts; i++) {
      const result = await loginPage.handleSweetAlert('confirm');
      if (result.handled) {
        alertCount++;
        console.log(`SweetAlert ${alertCount} handled:`, result);
      } else {
        break; // No more alerts
      }
    }
    
    console.log(`✅ Handled ${alertCount} SweetAlert(s)`);
    
    // Verify final state
    await dashboardPage.verifyDashboardLoaded();
  });
});
