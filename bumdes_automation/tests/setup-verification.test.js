const { test, expect } = require('@playwright/test');
const config = require('../utils/config');
const TestHelpers = require('../utils/helpers');

test.describe('Setup Verification Tests', () => {
  test('should verify test environment is working @smoke', async ({ page }) => {
    // Test that we can generate test data
    const testData = TestHelpers.generateTestData();
    expect(testData.user.name).toBeTruthy();
    expect(testData.business.businessName).toBeTruthy();
    expect(testData.transaction.amount).toBeGreaterThan(0);
    
    console.log('✅ Test data generation working');
    console.log('Sample data:', {
      userName: testData.user.name,
      businessName: testData.business.businessName,
      transactionAmount: TestHelpers.formatCurrency(testData.transaction.amount)
    });
  });

  test('should verify configuration is loaded @smoke', async () => {
    // Test that configuration is properly loaded
    expect(config.baseURL).toBeTruthy();
    expect(config.users.superAdmin.username).toBeTruthy();
    expect(config.selectors.loginForm).toBeTruthy();
    
    console.log('✅ Configuration loaded successfully');
    console.log('Base URL:', config.baseURL);
  });

  test('should verify helper functions work @smoke', async () => {
    // Test helper functions
    const randomString = TestHelpers.generateRandomString(10);
    const randomEmail = TestHelpers.generateRandomEmail();
    const currency = TestHelpers.formatCurrency(1000000);
    const date = TestHelpers.formatDate(new Date());
    
    expect(randomString).toHaveLength(10);
    expect(randomEmail).toContain('@');
    expect(currency).toContain('Rp');
    expect(date).toBeTruthy();
    
    console.log('✅ Helper functions working');
    console.log('Generated:', {
      randomString,
      randomEmail,
      currency,
      date
    });
  });

  test('should verify application is accessible @smoke', async ({ page }) => {
    try {
      // Try to access the application
      await page.goto(config.baseURL, { timeout: 10000 });
      
      // Check if page loads (should have some content)
      const title = await page.title();
      expect(title).toBeTruthy();
      
      console.log('✅ Application is accessible');
      console.log('Page title:', title);
      console.log('URL:', config.baseURL);
    } catch (error) {
      console.log('⚠️  Application not accessible:', error.message);
      console.log('Please make sure your BUMDes application is running at:', config.baseURL);
      
      // Don't fail the test, just warn
      expect(true).toBeTruthy(); // Always pass
    }
  });

  test('should verify browser capabilities @smoke', async ({ page, browserName }) => {
    // Test basic browser capabilities
    const userAgent = await page.evaluate(() => navigator.userAgent);
    const viewport = page.viewportSize();
    
    expect(userAgent).toBeTruthy();
    expect(viewport.width).toBeGreaterThan(0);
    expect(viewport.height).toBeGreaterThan(0);
    
    console.log('✅ Browser capabilities verified');
    console.log('Browser:', browserName);
    console.log('Viewport:', viewport);
    console.log('User Agent:', userAgent.substring(0, 50) + '...');
  });
});
