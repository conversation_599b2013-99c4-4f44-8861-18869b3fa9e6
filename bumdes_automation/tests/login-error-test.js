const { test, expect } = require('@playwright/test');
const LoginPage = require('../pages/LoginPage');

test.describe('Login Error Handling Tests', () => {
  let loginPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    await loginPage.goto();
  });

  test('should handle login error with .swal-icon--error selector @smoke', async () => {
    console.log('Testing login error handling with specific error selector...');
    
    // Try login with invalid credentials
    const loginResult = await loginPage.login('invalid_user', 'invalid_password');
    
    console.log('Login result:', loginResult);
    
    if (loginResult.success === false && loginResult.error) {
      console.log('✅ Error detected and handled:', loginResult.error);
      expect(loginResult.error.hasError).toBeTruthy();
      expect(loginResult.error.title || loginResult.error.content).toBeTruthy();
    } else {
      console.log('ℹ️ No specific error SweetAlert detected, checking for other error indicators...');
      
      // Check if still on login page (which indicates login failed)
      const currentURL = await loginPage.getCurrentURL();
      expect(currentURL).toContain('login');
      console.log('✅ Still on login page, indicating login failed as expected');
    }
  });

  test('should detect error icon specifically @regression', async ({ page }) => {
    console.log('Testing specific error icon detection...');
    
    // Fill invalid credentials
    await loginPage.fillUsername('invalid_user');
    await loginPage.fillPassword('invalid_password');
    await loginPage.clickLogin();
    
    // Wait for potential error
    await page.waitForTimeout(3000);
    
    // Check specifically for error icon
    const errorIconVisible = await page.isVisible('.swal-icon--error');
    console.log('Error icon (.swal-icon--error) visible:', errorIconVisible);
    
    if (errorIconVisible) {
      console.log('✅ Error icon detected');
      
      // Try to get error message
      const errorMessage = await loginPage.checkForLoginError();
      console.log('Error message details:', errorMessage);
      
      expect(errorMessage.hasError).toBeTruthy();
    } else {
      console.log('ℹ️ Error icon not found, checking for alternative error indicators...');
      
      // Check for other possible error selectors
      const alternativeErrorSelectors = [
        '.swal-icon--error',
        '.sweet-alert.error',
        '.sweet-alert[class*="error"]',
        '.swal-overlay .swal-icon--error',
        '[class*="swal-icon"][class*="error"]'
      ];
      
      let foundError = false;
      for (const selector of alternativeErrorSelectors) {
        const isVisible = await page.isVisible(selector);
        if (isVisible) {
          console.log(`✅ Found error with selector: ${selector}`);
          foundError = true;
          break;
        }
      }
      
      if (!foundError) {
        console.log('⚠️ No error icon found with any selector');
      }
    }
  });

  test('should test different invalid login scenarios @validation', async () => {
    console.log('Testing different invalid login scenarios...');
    
    const invalidScenarios = [
      { username: 'invalid_user', password: 'invalid_password', description: 'Both invalid' },
      { username: '', password: '', description: 'Both empty' },
      { username: 'admin', password: 'wrong_password', description: 'Valid user, wrong password' },
      { username: 'nonexistent', password: 'password123', description: 'Nonexistent user' }
    ];
    
    for (const scenario of invalidScenarios) {
      console.log(`Testing scenario: ${scenario.description}`);
      
      // Clear form and try login
      await loginPage.goto(); // Refresh page
      const loginResult = await loginPage.login(scenario.username, scenario.password);
      
      console.log(`Result for "${scenario.description}":`, loginResult);
      
      // Should either have error or still be on login page
      if (loginResult.success === false) {
        console.log(`✅ ${scenario.description} - Error properly detected`);
      } else {
        const currentURL = await loginPage.getCurrentURL();
        if (currentURL.includes('login')) {
          console.log(`✅ ${scenario.description} - Still on login page (login failed)`);
        } else {
          console.log(`⚠️ ${scenario.description} - Unexpected result`);
        }
      }
      
      // Wait between scenarios
      await loginPage.page.waitForTimeout(1000);
    }
  });

  test('should verify error message content @functionality', async () => {
    console.log('Testing error message content...');
    
    // Try invalid login
    await loginPage.fillUsername('invalid_user');
    await loginPage.fillPassword('invalid_password');
    await loginPage.clickLogin();
    
    // Wait for error
    await loginPage.page.waitForTimeout(3000);
    
    // Check for error details
    const errorResult = await loginPage.checkForLoginError();
    
    if (errorResult.hasError) {
      console.log('✅ Error message detected:', {
        title: errorResult.title,
        content: errorResult.content
      });
      
      // Verify error message contains relevant keywords
      const errorText = (errorResult.title + ' ' + errorResult.content).toLowerCase();
      const errorKeywords = ['error', 'gagal', 'salah', 'invalid', 'wrong', 'incorrect'];
      
      const hasErrorKeyword = errorKeywords.some(keyword => errorText.includes(keyword));
      if (hasErrorKeyword) {
        console.log('✅ Error message contains appropriate error keywords');
      } else {
        console.log('⚠️ Error message may not contain clear error indication');
        console.log('Error text:', errorText);
      }
    } else {
      console.log('ℹ️ No error message detected');
    }
  });

  test('should test error alert dismissal @functionality', async () => {
    console.log('Testing error alert dismissal...');
    
    // Try invalid login
    const loginResult = await loginPage.login('invalid_user', 'invalid_password');
    
    if (loginResult.success === false && loginResult.error && loginResult.error.hasError) {
      console.log('✅ Error alert was automatically dismissed');
      
      // Verify alert is no longer visible
      const alertStillVisible = await loginPage.isVisible(loginPage.selectors.sweetAlert);
      expect(alertStillVisible).toBeFalsy();
      
      console.log('✅ Error alert properly dismissed');
    } else {
      console.log('ℹ️ No error alert to dismiss or different error handling');
    }
    
    // Should be able to try login again
    await loginPage.fillUsername('test');
    await loginPage.fillPassword('test');
    console.log('✅ Can interact with form after error dismissal');
  });

  test('should handle rapid login attempts @stress', async () => {
    console.log('Testing rapid login attempts...');
    
    const attempts = 3;
    const results = [];
    
    for (let i = 0; i < attempts; i++) {
      console.log(`Attempt ${i + 1}/${attempts}`);
      
      const startTime = Date.now();
      const loginResult = await loginPage.login(`invalid${i}`, `password${i}`);
      const duration = Date.now() - startTime;
      
      results.push({
        attempt: i + 1,
        result: loginResult,
        duration: duration
      });
      
      console.log(`Attempt ${i + 1} completed in ${duration}ms`);
      
      // Small delay between attempts
      await loginPage.page.waitForTimeout(500);
    }
    
    console.log('All attempts completed:', results);
    
    // Verify all attempts failed as expected
    const allFailed = results.every(r => 
      r.result.success === false || 
      r.result.fallback === true
    );
    
    if (allFailed) {
      console.log('✅ All invalid login attempts properly handled');
    } else {
      console.log('⚠️ Some login attempts had unexpected results');
    }
  });

  test('should inspect error elements for debugging @debug', async ({ page }) => {
    console.log('Inspecting error elements for debugging...');
    
    // Try invalid login
    await loginPage.fillUsername('invalid_user');
    await loginPage.fillPassword('invalid_password');
    await loginPage.clickLogin();
    
    // Wait for any alerts
    await page.waitForTimeout(3000);
    
    // Get all elements that might be error-related
    const errorElements = await page.evaluate(() => {
      const elements = [];
      const allEls = document.querySelectorAll('*');
      
      allEls.forEach(el => {
        const className = el.className || '';
        const id = el.id || '';
        
        if (className.includes('swal') || 
            className.includes('sweet') || 
            className.includes('error') ||
            className.includes('alert') ||
            id.includes('swal') ||
            id.includes('error')) {
          elements.push({
            tagName: el.tagName,
            className: className,
            id: id,
            isVisible: el.offsetParent !== null,
            textContent: el.textContent?.substring(0, 100)
          });
        }
      });
      
      return elements;
    });
    
    console.log('Found error-related elements:');
    errorElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName} - Class: "${el.className}" - ID: "${el.id}" - Visible: ${el.isVisible}`);
      if (el.textContent) {
        console.log(`   Text: "${el.textContent}"`);
      }
    });
  });
});
