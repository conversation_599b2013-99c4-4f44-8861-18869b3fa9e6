const { test, expect } = require('@playwright/test');
const LoginPage = require('../pages/LoginPage');

test.describe('Success Alert Selector Tests', () => {
  let loginPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    await loginPage.goto();
  });

  test('should detect success alert without using h2 selector @smoke', async ({ page }) => {
    console.log('Testing success alert detection with flexible selectors...');
    
    // Fill and submit valid credentials
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for alert to appear
    await page.waitForTimeout(3000);
    
    // Check if success icon is visible
    const successIconVisible = await page.isVisible('.swal-icon--success');
    console.log('Success icon visible:', successIconVisible);
    
    if (successIconVisible) {
      console.log('✅ Success icon detected');
      
      // Test different title selectors (avoiding h2)
      const titleSelectors = [
        '.sweet-alert .swal-title',
        '.sweet-alert .sweet-alert-title',
        '.sweet-alert .title',
        '.sweet-alert div:first-child'
      ];
      
      let titleFound = false;
      let titleText = '';
      
      for (const selector of titleSelectors) {
        try {
          const isVisible = await page.isVisible(selector);
          if (isVisible) {
            titleText = await page.textContent(selector);
            console.log(`✅ Found title with selector: ${selector}`);
            console.log(`Title text: "${titleText}"`);
            titleFound = true;
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }
      
      // Test different content selectors
      const contentSelectors = [
        '.sweet-alert .swal-text',
        '.sweet-alert .sweet-alert-text',
        '.sweet-alert p',
        '.sweet-alert .content'
      ];
      
      let contentFound = false;
      let contentText = '';
      
      for (const selector of contentSelectors) {
        try {
          const isVisible = await page.isVisible(selector);
          if (isVisible) {
            contentText = await page.textContent(selector);
            console.log(`✅ Found content with selector: ${selector}`);
            console.log(`Content text: "${contentText}"`);
            contentFound = true;
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }
      
      if (titleFound || contentFound) {
        console.log('✅ Success alert content detected with flexible selectors');
      } else {
        console.log('⚠️ No success alert content found with any selector');
      }
      
      // Test the enhanced checkForLoginSuccess method
      const successResult = await loginPage.checkForLoginSuccess();
      console.log('Enhanced success detection result:', successResult);
      
      expect(successResult.hasSuccess).toBeTruthy();
      expect(successResult.buttonClicked).toBeTruthy();
      
    } else {
      console.log('ℹ️ No success icon found, may be different alert structure');
    }
  });

  test('should verify h2 selector is not used for success alerts @validation', async ({ page }) => {
    console.log('Verifying h2 selector is not used for success alerts...');
    
    // Perform login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for alert
    await page.waitForTimeout(3000);
    
    // Check if success alert is present
    const successIconVisible = await page.isVisible('.swal-icon--success');
    
    if (successIconVisible) {
      console.log('Success alert detected, checking selectors used...');
      
      // Check if h2 exists in success alert
      const h2InAlert = await page.isVisible('.sweet-alert h2');
      console.log('H2 element in alert:', h2InAlert);
      
      // Get all text content from the alert
      const alertText = await page.textContent('.sweet-alert');
      console.log('Full alert text:', alertText);
      
      // Test our enhanced method
      const successResult = await loginPage.checkForLoginSuccess();
      
      if (successResult.hasSuccess) {
        console.log('Success result:', {
          title: successResult.title,
          content: successResult.content,
          buttonClicked: successResult.buttonClicked
        });
        
        // Verify we got content without using h2
        if (successResult.title || successResult.content) {
          console.log('✅ Success alert content retrieved without h2 selector');
        } else {
          console.log('⚠️ No content retrieved, may need additional selectors');
        }
      }
    } else {
      console.log('ℹ️ No success alert to test');
    }
  });

  test('should inspect success alert structure @debug', async ({ page }) => {
    console.log('Inspecting success alert structure...');
    
    // Perform login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for alert
    await page.waitForTimeout(3000);
    
    // Check if success alert is present
    const successIconVisible = await page.isVisible('.swal-icon--success');
    
    if (successIconVisible) {
      console.log('Success alert detected, inspecting structure...');
      
      // Get all elements within the success alert
      const alertElements = await page.evaluate(() => {
        const alert = document.querySelector('.sweet-alert');
        if (!alert) return [];
        
        const elements = [];
        const allChildren = alert.querySelectorAll('*');
        
        allChildren.forEach(el => {
          if (el.textContent && el.textContent.trim()) {
            elements.push({
              tagName: el.tagName,
              className: el.className || '',
              id: el.id || '',
              textContent: el.textContent.trim(),
              isVisible: el.offsetParent !== null
            });
          }
        });
        
        return elements;
      });
      
      console.log('Success alert elements:');
      alertElements.forEach((el, index) => {
        console.log(`${index + 1}. ${el.tagName} - Class: "${el.className}" - ID: "${el.id}" - Visible: ${el.isVisible}`);
        console.log(`   Text: "${el.textContent}"`);
      });
      
      // Find the best selectors for title and content
      const titleElements = alertElements.filter(el => 
        el.className.includes('title') || 
        el.className.includes('swal-title') ||
        el.tagName === 'H1' ||
        el.tagName === 'H2' ||
        el.tagName === 'H3'
      );
      
      const contentElements = alertElements.filter(el => 
        el.className.includes('text') || 
        el.className.includes('content') ||
        el.tagName === 'P' ||
        el.tagName === 'DIV'
      );
      
      console.log('Potential title elements:', titleElements);
      console.log('Potential content elements:', contentElements);
      
      // Dismiss the alert
      await loginPage.checkForLoginSuccess();
    } else {
      console.log('ℹ️ No success alert to inspect');
    }
  });

  test('should test alternative success detection methods @functionality', async ({ page }) => {
    console.log('Testing alternative success detection methods...');
    
    // Perform login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for response
    await page.waitForTimeout(3000);
    
    // Method 1: Check for success icon
    const method1 = await page.isVisible('.swal-icon--success');
    console.log('Method 1 (success icon):', method1);
    
    // Method 2: Check for success class
    const method2 = await page.isVisible('.sweet-alert.success');
    console.log('Method 2 (success class):', method2);
    
    // Method 3: Check for green color or success indicators
    const method3 = await page.evaluate(() => {
      const alert = document.querySelector('.sweet-alert');
      if (!alert) return false;
      
      const style = window.getComputedStyle(alert);
      const text = alert.textContent.toLowerCase();
      
      return text.includes('berhasil') || 
             text.includes('success') || 
             text.includes('sukses') ||
             style.color.includes('green') ||
             alert.className.includes('success');
    });
    console.log('Method 3 (content/style check):', method3);
    
    // Method 4: Check URL change (redirect started)
    const currentURL = page.url();
    const method4 = currentURL.includes('dashboard') || !currentURL.includes('login');
    console.log('Method 4 (URL change):', method4);
    
    // Use our enhanced detection
    const enhancedResult = await loginPage.checkForLoginSuccess();
    console.log('Enhanced detection result:', enhancedResult);
    
    // At least one method should detect success
    const anyMethodSuccess = method1 || method2 || method3 || method4 || enhancedResult.hasSuccess;
    expect(anyMethodSuccess).toBeTruthy();
    
    console.log('✅ Success detected by at least one method');
  });

  test('should compare error vs success alert selectors @comparison', async ({ page }) => {
    console.log('Comparing error vs success alert selectors...');
    
    // First test error alert
    console.log('Testing error alert...');
    await loginPage.fillUsername('invalid_user');
    await loginPage.fillPassword('invalid_password');
    await loginPage.clickLogin();
    await page.waitForTimeout(3000);
    
    const errorIconVisible = await page.isVisible('.swal-icon--error');
    if (errorIconVisible) {
      console.log('Error alert detected');
      
      const errorResult = await loginPage.checkForLoginError();
      console.log('Error detection result:', {
        hasError: errorResult.hasError,
        title: errorResult.title,
        content: errorResult.content
      });
    }
    
    // Reset and test success alert
    console.log('Testing success alert...');
    await loginPage.goto();
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    await page.waitForTimeout(3000);
    
    const successIconVisible = await page.isVisible('.swal-icon--success');
    if (successIconVisible) {
      console.log('Success alert detected');
      
      const successResult = await loginPage.checkForLoginSuccess();
      console.log('Success detection result:', {
        hasSuccess: successResult.hasSuccess,
        title: successResult.title,
        content: successResult.content,
        buttonClicked: successResult.buttonClicked
      });
    }
    
    console.log('✅ Comparison completed');
  });
});
