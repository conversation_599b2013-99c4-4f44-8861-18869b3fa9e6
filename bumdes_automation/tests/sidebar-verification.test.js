const { test, expect } = require('@playwright/test');
const LoginPage = require('../pages/LoginPage');
const DashboardPage = require('../pages/DashboardPage');

test.describe('Sidebar Verification Tests', () => {
  let loginPage;
  let dashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    await loginPage.goto();
  });

  test('should verify sidebar appears after Super Admin login @smoke', async () => {
    console.log('Testing sidebar appearance after Super Admin login...');
    
    // Perform login
    await loginPage.loginAsSuperAdmin();
    
    // Handle SweetAlert if it appears
    const alertResult = await loginPage.handleSweetAlert('confirm');
    if (alertResult.handled) {
      console.log('✅ SweetAlert handled:', alertResult);
    }
    
    // Wait for page to load
    await loginPage.page.waitForTimeout(3000);
    
    // Check current URL
    const currentURL = await loginPage.getCurrentURL();
    console.log('Current URL:', currentURL);
    
    // Verify sidebar is visible
    const sidebarVisible = await dashboardPage.isVisible(dashboardPage.selectors.sidebar);
    console.log('Sidebar visible:', sidebarVisible);
    
    if (sidebarVisible) {
      console.log('✅ Sidebar successfully detected');
      await dashboardPage.verifyElementVisible(dashboardPage.selectors.sidebar);
    } else {
      console.log('❌ Sidebar not found, checking for alternative selectors...');
      
      // Try alternative sidebar selectors
      const alternativeSelectors = [
        '.sidebar',
        '#sidebar',
        '.side-nav',
        '.navigation',
        '.menu-sidebar',
        '[class*="sidebar"]',
        '[id*="sidebar"]'
      ];
      
      let foundSidebar = false;
      for (const selector of alternativeSelectors) {
        const isVisible = await dashboardPage.isVisible(selector);
        if (isVisible) {
          console.log(`✅ Found sidebar with selector: ${selector}`);
          foundSidebar = true;
          break;
        }
      }
      
      if (!foundSidebar) {
        console.log('⚠️ No sidebar found with any selector');
      }
    }
    
    // Verify dashboard is loaded
    if (currentURL.includes('dashboard')) {
      console.log('✅ Successfully on dashboard page');
    } else {
      console.log('⚠️ Not on dashboard page, current URL:', currentURL);
    }
  });

  test('should verify sidebar appears after BUMDes login @smoke', async () => {
    console.log('Testing sidebar appearance after BUMDes login...');
    
    // Perform login
    await loginPage.loginAsBumdes();
    
    // Handle SweetAlert if it appears
    const alertResult = await loginPage.handleSweetAlert('confirm');
    if (alertResult.handled) {
      console.log('✅ SweetAlert handled:', alertResult);
    }
    
    // Wait for page to load
    await loginPage.page.waitForTimeout(3000);
    
    // Verify sidebar is visible
    const sidebarVisible = await dashboardPage.isVisible(dashboardPage.selectors.sidebar);
    console.log('Sidebar visible after BUMDes login:', sidebarVisible);
    
    if (sidebarVisible) {
      console.log('✅ Sidebar successfully detected for BUMDes user');
      await dashboardPage.verifyElementVisible(dashboardPage.selectors.sidebar);
    }
    
    // Check current URL
    const currentURL = await loginPage.getCurrentURL();
    console.log('BUMDes user current URL:', currentURL);
  });

  test('should verify sidebar appears after BUMDes User login @smoke', async () => {
    console.log('Testing sidebar appearance after BUMDes User login...');
    
    // Perform login
    await loginPage.loginAsBumdesUser();
    
    // Handle SweetAlert if it appears
    const alertResult = await loginPage.handleSweetAlert('confirm');
    if (alertResult.handled) {
      console.log('✅ SweetAlert handled:', alertResult);
    }
    
    // Wait for page to load
    await loginPage.page.waitForTimeout(3000);
    
    // Verify sidebar is visible
    const sidebarVisible = await dashboardPage.isVisible(dashboardPage.selectors.sidebar);
    console.log('Sidebar visible after BUMDes User login:', sidebarVisible);
    
    if (sidebarVisible) {
      console.log('✅ Sidebar successfully detected for BUMDes User');
      await dashboardPage.verifyElementVisible(dashboardPage.selectors.sidebar);
    }
    
    // Check current URL
    const currentURL = await loginPage.getCurrentURL();
    console.log('BUMDes User current URL:', currentURL);
  });

  test('should inspect page elements after login @debug', async ({ page }) => {
    console.log('Inspecting page elements after login...');
    
    // Perform login
    await loginPage.loginAsSuperAdmin();
    
    // Handle SweetAlert
    await loginPage.handleSweetAlert('confirm');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Get all elements that might be navigation related
    const navigationElements = await page.evaluate(() => {
      const elements = [];
      const allEls = document.querySelectorAll('*');
      
      allEls.forEach(el => {
        const className = el.className || '';
        const id = el.id || '';
        
        if (className.includes('sidebar') || 
            className.includes('nav') || 
            className.includes('menu') ||
            id.includes('sidebar') ||
            id.includes('nav') ||
            id.includes('menu')) {
          elements.push({
            tagName: el.tagName,
            className: className,
            id: id,
            isVisible: el.offsetParent !== null,
            textContent: el.textContent?.substring(0, 50)
          });
        }
      });
      
      return elements;
    });
    
    console.log('Found navigation-related elements:');
    navigationElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName} - Class: "${el.className}" - ID: "${el.id}" - Visible: ${el.isVisible}`);
      if (el.textContent) {
        console.log(`   Text: "${el.textContent}"`);
      }
    });
    
    // Check page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Check current URL
    const url = page.url();
    console.log('Current URL:', url);
  });

  test('should test complete login flow with sidebar verification @integration', async () => {
    console.log('Testing complete login flow with sidebar verification...');
    
    const startTime = Date.now();
    
    // Step 1: Login
    console.log('Step 1: Performing login...');
    await loginPage.loginAsSuperAdmin();
    
    // Step 2: Handle SweetAlert
    console.log('Step 2: Handling SweetAlert...');
    const alertResult = await loginPage.handleSweetAlert('confirm');
    console.log('SweetAlert result:', alertResult);
    
    // Step 3: Wait for redirect
    console.log('Step 3: Waiting for redirect...');
    await loginPage.page.waitForTimeout(3000);
    
    // Step 4: Verify URL
    console.log('Step 4: Verifying URL...');
    const currentURL = await loginPage.getCurrentURL();
    console.log('Current URL:', currentURL);
    
    // Step 5: Verify sidebar
    console.log('Step 5: Verifying sidebar...');
    const sidebarVisible = await dashboardPage.isVisible(dashboardPage.selectors.sidebar);
    console.log('Sidebar visible:', sidebarVisible);
    
    // Step 6: Complete verification
    if (currentURL.includes('dashboard') && sidebarVisible) {
      console.log('✅ Complete login flow successful');
      await dashboardPage.verifyDashboardLoaded();
    } else {
      console.log('⚠️ Login flow incomplete');
      console.log('- Dashboard URL:', currentURL.includes('dashboard'));
      console.log('- Sidebar visible:', sidebarVisible);
    }
    
    const totalTime = Date.now() - startTime;
    console.log(`Total login flow time: ${totalTime}ms`);
    
    // Performance check
    expect(totalTime).toBeLessThan(20000); // 20 seconds max
  });
});
