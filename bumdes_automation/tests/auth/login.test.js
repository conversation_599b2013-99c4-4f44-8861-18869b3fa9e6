const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const DashboardPage = require('../../pages/DashboardPage');
const config = require('../../utils/config');

test.describe('Authentication Tests', () => {
  let loginPage;
  let dashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    await loginPage.goto();
  });

  test.describe('Login Functionality', () => {
    test('should display login form correctly @smoke', async () => {
      await loginPage.verifyLoginFormVisible();
    });

    test('should login successfully with valid Super Admin credentials @smoke', async () => {
      await loginPage.loginAsSuperAdmin();
      await loginPage.verifySuccessfulLogin();
      await dashboardPage.verifyDashboardLoaded();
    });

    test('should login successfully with valid BUMDes credentials @smoke', async () => {
      await loginPage.loginAsBumdes();
      await loginPage.verifySuccessfulLogin();
      await dashboardPage.verifyDashboardLoaded();
    });

    test('should login successfully with valid BUMDes User credentials @smoke', async () => {
      await loginPage.loginAsBumdesUser();
      await loginPage.verifySuccessfulLogin();
      await dashboardPage.verifyDashboardLoaded();
    });

    test('should show error message with invalid credentials @regression', async () => {
      const errorMessage = await loginPage.testInvalidLogin('wronguser', 'wrongpass');
      expect(errorMessage).toBeTruthy();
    });

    test('should show validation error with empty credentials @regression', async () => {
      const hasError = await loginPage.testEmptyLogin();
      expect(hasError).toBeTruthy();
    });

    test('should prevent SQL injection attempts @security', async () => {
      const results = await loginPage.testSQLInjectionLogin();
      
      // All SQL injection attempts should be blocked
      for (const result of results) {
        expect(result.blocked).toBeTruthy();
      }
    });

    test('should have proper accessibility features @accessibility', async () => {
      await loginPage.verifyPageAccessibility();
    });

    test('should toggle password visibility if feature exists @ui', async () => {
      const hasToggle = await loginPage.testPasswordVisibilityToggle();
      // This test passes regardless of whether toggle exists
      console.log('Password visibility toggle available:', hasToggle);
    });
  });

  test.describe('Navigation Tests', () => {
    test('should navigate to registration page if link exists @navigation', async () => {
      await loginPage.clickRegisterLink();
      // Verify if redirected to registration page
      const currentURL = await loginPage.getCurrentURL();
      if (currentURL.includes('register')) {
        expect(currentURL).toContain('register');
      }
    });

    test('should navigate to forgot password page if link exists @navigation', async () => {
      await loginPage.clickForgotPasswordLink();
      // Verify if redirected to forgot password page
      const currentURL = await loginPage.getCurrentURL();
      if (currentURL.includes('forgot')) {
        expect(currentURL).toContain('forgot');
      }
    });
  });

  test.describe('Session Management', () => {
    test('should maintain session after successful login @session', async () => {
      await loginPage.loginAsSuperAdmin();
      await dashboardPage.verifyDashboardLoaded();
      
      // Navigate to another page and back
      await dashboardPage.navigateToMenuItem('Master BUMDes');
      await dashboardPage.goto();
      
      // Should still be logged in
      await dashboardPage.verifyDashboardLoaded();
    });

    test('should logout successfully @session', async () => {
      await loginPage.loginAsSuperAdmin();
      await dashboardPage.verifyDashboardLoaded();
      
      await dashboardPage.logout();
      
      // Should be redirected to login page
      const currentURL = await loginPage.getCurrentURL();
      expect(currentURL).toContain('login');
    });
  });

  test.describe('Security Tests', () => {
    test('should not allow access to protected pages without login @security', async ({ page }) => {
      // Try to access dashboard directly without login
      await page.goto(config.baseURL + config.urls.dashboard);
      
      // Should be redirected to login page
      const currentURL = page.url();
      expect(currentURL).toContain('login');
    });

    test('should handle concurrent login attempts @security', async ({ browser }) => {
      const context1 = await browser.newContext();
      const context2 = await browser.newContext();
      
      const page1 = await context1.newPage();
      const page2 = await context2.newPage();
      
      const loginPage1 = new LoginPage(page1);
      const loginPage2 = new LoginPage(page2);
      
      await loginPage1.goto();
      await loginPage2.goto();
      
      // Try to login with same credentials simultaneously
      await Promise.all([
        loginPage1.loginAsSuperAdmin(),
        loginPage2.loginAsSuperAdmin()
      ]);
      
      // Both should be able to login (or handle appropriately)
      const url1 = await page1.url();
      const url2 = await page2.url();
      
      expect(url1).toContain('dashboard');
      expect(url2).toContain('dashboard');
      
      await context1.close();
      await context2.close();
    });
  });

  test.describe('Performance Tests', () => {
    test('should load login page within acceptable time @performance', async ({ page }) => {
      const startTime = Date.now();
      await loginPage.goto();
      await loginPage.verifyLoginFormVisible();
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(5000); // 5 seconds threshold
    });

    test('should complete login process within acceptable time @performance', async () => {
      const startTime = Date.now();
      await loginPage.loginAsSuperAdmin();
      await dashboardPage.verifyDashboardLoaded();
      const loginTime = Date.now() - startTime;
      
      expect(loginTime).toBeLessThan(10000); // 10 seconds threshold
    });
  });

  test.describe('Cross-browser Compatibility', () => {
    test('should work consistently across different browsers @compatibility', async ({ browserName }) => {
      console.log(`Testing on browser: ${browserName}`);
      
      await loginPage.verifyLoginFormVisible();
      await loginPage.loginAsSuperAdmin();
      await dashboardPage.verifyDashboardLoaded();
      
      // Browser-specific assertions can be added here
      expect(browserName).toBeTruthy();
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should be responsive on mobile devices @mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await loginPage.verifyLoginFormVisible();
      
      // Check if form is still usable on mobile
      await loginPage.fillUsername('test');
      await loginPage.fillPassword('test');
      
      // Form should be functional
      const usernameValue = await page.inputValue(loginPage.selectors.usernameInput);
      const passwordValue = await page.inputValue(loginPage.selectors.passwordInput);
      
      expect(usernameValue).toBe('test');
      expect(passwordValue).toBe('test');
    });
  });
});
