const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const DashboardPage = require('../../pages/DashboardPage');
const TestHelpers = require('../../utils/helpers');

test.describe('Dashboard Tests', () => {
  let loginPage;
  let dashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);

    // Login as Super Admin for most tests
    await loginPage.goto();
    await loginPage.loginAsSuperAdmin();
    await dashboardPage.verifyDashboardLoaded();
  });

  test.describe('Dashboard Layout', () => {
    test('should display dashboard correctly for Super Admin @smoke', async () => {
      await dashboardPage.verifyDashboardCards();
      await dashboardPage.verifyChartsLoaded();
      await dashboardPage.verifyMonitoringSection();
    });

    test('should display user information correctly @smoke', async () => {
      const userInfo = await dashboardPage.verifyUserInfo();
      expect(userInfo).toBeTruthy();
    });

    test('should display navigation menu correctly @smoke', async () => {
      await dashboardPage.verifyElementVisible(dashboardPage.selectors.sidebar);
      // Sidebar is the main navigation element after successful login
    });
  });

  test.describe('Dashboard Data', () => {
    test('should display financial summary cards @regression', async () => {
      const modal = await dashboardPage.getTotalModal();
      const omset = await dashboardPage.getTotalOmset();
      const laba = await dashboardPage.getTotalLaba();

      // Values should be displayed (can be 0 or formatted currency)
      console.log('Financial Data:', { modal, omset, laba });
    });

    test('should validate data consistency @regression', async () => {
      const dataConsistency = await dashboardPage.verifyDataConsistency();

      expect(dataConsistency.modal.valid).toBeTruthy();
      expect(dataConsistency.omset.valid).toBeTruthy();
      expect(dataConsistency.laba.valid).toBeTruthy();
    });

    test('should display BUMDes count for Super Admin @regression', async () => {
      const totalBumdes = await dashboardPage.getTotalBumdes();
      console.log('Total BUMDes:', totalBumdes);
    });
  });

  test.describe('Dashboard Filters', () => {
    test('should change data when year filter is applied @regression', async () => {
      const currentYear = new Date().getFullYear();
      const previousYear = currentYear - 1;

      // Get current data
      const currentData = await dashboardPage.getTotalOmset();

      // Change year filter
      await dashboardPage.changeYearFilter(previousYear.toString());

      // Get new data
      const newData = await dashboardPage.getTotalOmset();

      // Data might be different (or same if no data for previous year)
      console.log('Year filter test:', { currentData, newData });
    });

    test('should change data when BUMDes filter is applied @regression', async () => {
      // This test is for Super Admin only
      await dashboardPage.changeBumdesFilter('1');

      // Verify data loads after filter change
      const omset = await dashboardPage.getTotalOmset();
      console.log('BUMDes filter applied, omset:', omset);
    });
  });

  test.describe('Dashboard Navigation', () => {
    test('should navigate to transaction page from quick action @navigation', async () => {
      await dashboardPage.clickAddTransaction();
      await dashboardPage.verifyCurrentURL('transaction');
    });

    test('should navigate to reports page from quick action @navigation', async () => {
      await dashboardPage.clickViewReports();
      await dashboardPage.verifyCurrentURL('reports');
    });

    test('should navigate to all menu items correctly @navigation', async () => {
      const menuItems = [
        'Master BUMDes',
        'Unit Usaha',
        'Pengguna BUMDes',
        'Transaksi',
        'Saldo Awal',
        'Laporan',
        'History Transaksi'
      ];

      for (const menuItem of menuItems) {
        await dashboardPage.navigateToMenuItem(menuItem);
        // Navigate back to dashboard
        await dashboardPage.goto();
        await dashboardPage.verifyDashboardLoaded();
      }
    });
  });

  test.describe('Role-based Dashboard', () => {
    test('should display appropriate dashboard for BUMDes role @access-control', async ({ page }) => {
      // Login as BUMDes user
      const bumdesLoginPage = new LoginPage(page);
      const bumdesDashboardPage = new DashboardPage(page);

      await bumdesLoginPage.goto();
      await bumdesLoginPage.loginAsBumdes();
      await bumdesDashboardPage.verifyDashboardLoaded();

      // BUMDes should not see monitoring section
      const hasMonitoring = await bumdesDashboardPage.verifyMonitoringSection();
      expect(hasMonitoring).toBeFalsy();

      // Should see BUMDes overview
      const hasBumdesOverview = await bumdesDashboardPage.verifyBumdesOverviewSection();
      expect(hasBumdesOverview).toBeTruthy();
    });

    test('should display appropriate dashboard for BUMDes User role @access-control', async ({ page }) => {
      // Login as BUMDes User
      const userLoginPage = new LoginPage(page);
      const userDashboardPage = new DashboardPage(page);

      await userLoginPage.goto();
      await userLoginPage.loginAsBumdesUser();
      await userDashboardPage.verifyDashboardLoaded();

      // BUMDes User should have limited access
      const hasMonitoring = await userDashboardPage.verifyMonitoringSection();
      expect(hasMonitoring).toBeFalsy();
    });
  });

  test.describe('Dashboard Performance', () => {
    test('should load dashboard within acceptable time @performance', async () => {
      const performance = await dashboardPage.testPerformance();

      expect(performance.acceptable).toBeTruthy();
      console.log('Dashboard load time:', performance.loadTime, 'ms');
    });

    test('should handle large datasets efficiently @performance', async () => {
      // Test with monitoring table data
      const tableRowCount = await dashboardPage.getMonitoringTableData();

      if (tableRowCount > 0) {
        // Measure time to load table
        const startTime = Date.now();
        await dashboardPage.goto();
        await dashboardPage.verifyDashboardLoaded();
        const loadTime = Date.now() - startTime;

        expect(loadTime).toBeLessThan(10000); // 10 seconds for large datasets
        console.log(`Loaded ${tableRowCount} rows in ${loadTime}ms`);
      }
    });
  });

  test.describe('Dashboard Responsiveness', () => {
    test('should be responsive on different screen sizes @responsive', async () => {
      const responsiveResults = await dashboardPage.testResponsiveness();

      // Verify that dashboard elements are visible on all tested viewports
      for (const result of responsiveResults) {
        expect(result.sidebarVisible).toBeTruthy();
        console.log(`Viewport ${result.viewport.width}x${result.viewport.height}:`, result);
      }
    });

    test('should maintain functionality on mobile @mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      await dashboardPage.goto();
      await dashboardPage.verifyDashboardLoaded();

      // Check if cards are still visible and functional
      await dashboardPage.verifyDashboardCards();
    });
  });

  test.describe('Dashboard Charts', () => {
    test('should display charts correctly @charts', async () => {
      await dashboardPage.verifyChartsLoaded();

      // Check if charts have data
      const hasOmsetChart = await dashboardPage.isVisible(dashboardPage.selectors.omsetChart);
      const hasLabaChart = await dashboardPage.isVisible(dashboardPage.selectors.labaChart);

      console.log('Charts visibility:', { omset: hasOmsetChart, laba: hasLabaChart });
    });

    test('should update charts when filters change @charts', async () => {
      // Apply year filter and check if charts update
      const currentYear = new Date().getFullYear();
      await dashboardPage.changeYearFilter(currentYear.toString());

      // Charts should still be visible after filter change
      await dashboardPage.verifyChartsLoaded();
    });
  });

  test.describe('Dashboard Error Handling', () => {
    test('should handle network errors gracefully @error-handling', async ({ page }) => {
      // Simulate network failure
      await page.route('**/*', route => route.abort());

      try {
        await dashboardPage.goto();
      } catch (error) {
        // Should handle network errors gracefully
        console.log('Network error handled:', error.message);
      }

      // Restore network
      await page.unroute('**/*');
    });

    test('should display appropriate message when no data available @error-handling', async () => {
      // This test checks if dashboard handles empty data states
      const modal = await dashboardPage.getTotalModal();
      const omset = await dashboardPage.getTotalOmset();
      const laba = await dashboardPage.getTotalLaba();

      // Should display some value (even if 0 or "-")
      expect(modal !== null || omset !== null || laba !== null).toBeTruthy();
    });
  });
});
