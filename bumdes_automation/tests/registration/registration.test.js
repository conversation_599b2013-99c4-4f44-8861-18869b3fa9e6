const { test, expect } = require('@playwright/test');
const BasePage = require('../../pages/BasePage');
const TestHelpers = require('../../utils/helpers');

class RegistrationPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      // Registration form
      registrationForm: '#frmRegister',
      businessOwnerInput: 'input[name="business_owner"]',
      businessNameInput: 'input[name="business_name"]',
      addressInput: 'textarea[name="address"]',
      usernameInput: 'input[name="username"]',
      passwordInput: 'input[name="password"]',
      confirmPasswordInput: 'input[name="confirm_password"]',
      
      // Location selectors
      provinceSelect: 'select[name="province_id"]',
      citySelect: 'select[name="city_id"]',
      districtSelect: 'select[name="district_id"]',
      villageSelect: 'select[name="village_id"]',
      
      // Work unit selection
      workunitSelect: 'select[name="workunit_ids[]"]',
      
      // Buttons
      submitBtn: 'button[type="submit"]',
      loginLink: 'a[href*="login"]',
      
      // Messages
      successMessage: '.alert-success',
      errorMessage: '.alert-danger'
    };
  }
  
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.register);
  }
  
  async fillRegistrationForm(registrationData) {
    // Fill business owner
    if (registrationData.businessOwner) {
      await this.fill(this.selectors.businessOwnerInput, registrationData.businessOwner);
    }
    
    // Fill business name
    if (registrationData.businessName) {
      await this.fill(this.selectors.businessNameInput, registrationData.businessName);
    }
    
    // Fill address
    if (registrationData.address) {
      await this.fill(this.selectors.addressInput, registrationData.address);
    }
    
    // Fill username
    if (registrationData.username) {
      await this.fill(this.selectors.usernameInput, registrationData.username);
    }
    
    // Fill password
    if (registrationData.password) {
      await this.fill(this.selectors.passwordInput, registrationData.password);
    }
    
    // Fill confirm password
    if (registrationData.confirmPassword) {
      await this.fill(this.selectors.confirmPasswordInput, registrationData.confirmPassword);
    }
    
    // Select location
    if (registrationData.provinceId) {
      await this.selectOption(this.selectors.provinceSelect, registrationData.provinceId);
      await this.page.waitForTimeout(1000); // Wait for cascade
    }
    
    if (registrationData.cityId) {
      await this.selectOption(this.selectors.citySelect, registrationData.cityId);
      await this.page.waitForTimeout(1000);
    }
    
    if (registrationData.districtId) {
      await this.selectOption(this.selectors.districtSelect, registrationData.districtId);
      await this.page.waitForTimeout(1000);
    }
    
    if (registrationData.villageId) {
      await this.selectOption(this.selectors.villageSelect, registrationData.villageId);
    }
    
    // Select work units (multiple selection)
    if (registrationData.workunitIds && Array.isArray(registrationData.workunitIds)) {
      for (const workunitId of registrationData.workunitIds) {
        await this.page.selectOption(this.selectors.workunitSelect, workunitId);
      }
    }
  }
  
  async submitRegistration() {
    await this.click(this.selectors.submitBtn);
    await this.helpers.waitForPageLoad(this.page);
  }
  
  async registerBumdes(registrationData) {
    await this.fillRegistrationForm(registrationData);
    await this.submitRegistration();
  }
  
  async verifySuccessMessage() {
    await this.waitForElement(this.selectors.successMessage);
    const message = await this.getText(this.selectors.successMessage);
    return message;
  }
  
  async verifyErrorMessage() {
    await this.waitForElement(this.selectors.errorMessage);
    const message = await this.getText(this.selectors.errorMessage);
    return message;
  }
  
  async testFormValidation() {
    // Try to submit empty form
    await this.submitRegistration();
    
    // Check for validation messages
    const hasValidationErrors = await this.isVisible('.invalid-feedback') || 
                               await this.isVisible('.alert-danger');
    
    return hasValidationErrors;
  }
  
  async testPasswordMismatch() {
    const testData = TestHelpers.generateTestData().business;
    testData.password = 'password123';
    testData.confirmPassword = 'differentpassword';
    
    await this.fillRegistrationForm(testData);
    await this.submitRegistration();
    
    // Check for password mismatch error
    const hasError = await this.isVisible('.alert-danger');
    
    if (hasError) {
      const errorText = await this.getText('.alert-danger');
      return errorText.includes('password') || errorText.includes('tidak sama');
    }
    
    return false;
  }
  
  async testUsernameUniqueness(existingUsername) {
    const testData = TestHelpers.generateTestData().business;
    testData.username = existingUsername;
    testData.password = 'password123';
    testData.confirmPassword = 'password123';
    
    await this.registerBumdes(testData);
    
    // Check for duplicate username error
    const hasError = await this.isVisible('.alert-danger');
    
    if (hasError) {
      const errorText = await this.getText('.alert-danger');
      return errorText.includes('username') || errorText.includes('sudah digunakan');
    }
    
    return false;
  }
  
  async testCascadingLocationSelection() {
    // Select province
    await this.selectOption(this.selectors.provinceSelect, '1');
    await this.page.waitForTimeout(2000);
    
    // City dropdown should be populated
    const cityOptions = await this.page.locator(this.selectors.citySelect + ' option').count();
    
    if (cityOptions > 1) {
      // Select city
      await this.selectOption(this.selectors.citySelect, '1');
      await this.page.waitForTimeout(2000);
      
      // District dropdown should be populated
      const districtOptions = await this.page.locator(this.selectors.districtSelect + ' option').count();
      
      if (districtOptions > 1) {
        // Select district
        await this.selectOption(this.selectors.districtSelect, '1');
        await this.page.waitForTimeout(2000);
        
        // Village dropdown should be populated
        const villageOptions = await this.page.locator(this.selectors.villageSelect + ' option').count();
        
        return {
          provinceWorks: true,
          cityWorks: cityOptions > 1,
          districtWorks: districtOptions > 1,
          villageWorks: villageOptions > 1
        };
      }
    }
    
    return {
      provinceWorks: cityOptions > 1,
      cityWorks: false,
      districtWorks: false,
      villageWorks: false
    };
  }
}

test.describe('BUMDes Registration Tests', () => {
  let registrationPage;

  test.beforeEach(async ({ page }) => {
    registrationPage = new RegistrationPage(page);
    await registrationPage.goto();
  });

  test.describe('Registration Form Display', () => {
    test('should display registration form correctly @smoke', async () => {
      // Verify all form elements are visible
      await registrationPage.verifyElementVisible(registrationPage.selectors.registrationForm);
      await registrationPage.verifyElementVisible(registrationPage.selectors.businessOwnerInput);
      await registrationPage.verifyElementVisible(registrationPage.selectors.businessNameInput);
      await registrationPage.verifyElementVisible(registrationPage.selectors.addressInput);
      await registrationPage.verifyElementVisible(registrationPage.selectors.usernameInput);
      await registrationPage.verifyElementVisible(registrationPage.selectors.passwordInput);
      await registrationPage.verifyElementVisible(registrationPage.selectors.confirmPasswordInput);
      await registrationPage.verifyElementVisible(registrationPage.selectors.submitBtn);
    });

    test('should display location selection dropdowns @smoke', async () => {
      await registrationPage.verifyElementVisible(registrationPage.selectors.provinceSelect);
      await registrationPage.verifyElementVisible(registrationPage.selectors.citySelect);
      await registrationPage.verifyElementVisible(registrationPage.selectors.districtSelect);
      await registrationPage.verifyElementVisible(registrationPage.selectors.villageSelect);
    });

    test('should display work unit selection @smoke', async () => {
      await registrationPage.verifyElementVisible(registrationPage.selectors.workunitSelect);
    });
  });

  test.describe('Registration Functionality', () => {
    test('should register new BUMDes successfully @smoke', async () => {
      const testData = {
        businessOwner: TestHelpers.generateTestData().business.ownerName,
        businessName: TestHelpers.generateTestData().business.businessName,
        address: TestHelpers.generateTestData().business.address,
        username: TestHelpers.generateRandomString(8).toLowerCase(),
        password: 'password123',
        confirmPassword: 'password123',
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1']
      };
      
      await registrationPage.registerBumdes(testData);
      
      // Should show success message
      const successMessage = await registrationPage.verifySuccessMessage();
      expect(successMessage).toBeTruthy();
      console.log('Registration success message:', successMessage);
    });

    test('should handle registration with multiple work units @regression', async () => {
      const testData = {
        businessOwner: TestHelpers.generateTestData().business.ownerName,
        businessName: TestHelpers.generateTestData().business.businessName,
        address: TestHelpers.generateTestData().business.address,
        username: TestHelpers.generateRandomString(8).toLowerCase(),
        password: 'password123',
        confirmPassword: 'password123',
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1', '2'] // Multiple work units
      };
      
      await registrationPage.registerBumdes(testData);
      
      // Should handle multiple work units successfully
      const hasSuccess = await registrationPage.isVisible(registrationPage.selectors.successMessage);
      const hasError = await registrationPage.isVisible(registrationPage.selectors.errorMessage);
      
      console.log('Multiple work units registration:', { success: hasSuccess, error: hasError });
    });
  });

  test.describe('Registration Validation', () => {
    test('should validate required fields @validation', async () => {
      const hasValidation = await registrationPage.testFormValidation();
      expect(hasValidation).toBeTruthy();
    });

    test('should validate password confirmation @validation', async () => {
      const hasPasswordMismatch = await registrationPage.testPasswordMismatch();
      expect(hasPasswordMismatch).toBeTruthy();
    });

    test('should validate username uniqueness @validation', async () => {
      // First register a user
      const testData = {
        businessOwner: TestHelpers.generateTestData().business.ownerName,
        businessName: TestHelpers.generateTestData().business.businessName,
        address: TestHelpers.generateTestData().business.address,
        username: 'uniquetest' + TestHelpers.generateRandomString(5),
        password: 'password123',
        confirmPassword: 'password123',
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1']
      };
      
      await registrationPage.registerBumdes(testData);
      
      // Try to register with same username
      await registrationPage.goto(); // Refresh page
      const isDuplicate = await registrationPage.testUsernameUniqueness(testData.username);
      expect(isDuplicate).toBeTruthy();
    });

    test('should validate password strength @validation', async () => {
      const testData = {
        businessOwner: 'Test Owner',
        businessName: 'Test Business',
        address: 'Test Address',
        username: 'testuser',
        password: '123', // Weak password
        confirmPassword: '123',
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1']
      };
      
      await registrationPage.registerBumdes(testData);
      
      // Should show password strength error
      const hasError = await registrationPage.isVisible(registrationPage.selectors.errorMessage);
      if (hasError) {
        const errorText = await registrationPage.getText(registrationPage.selectors.errorMessage);
        expect(errorText.toLowerCase()).toContain('password');
      }
    });
  });

  test.describe('Location Selection', () => {
    test('should test cascading location selection @functionality', async () => {
      const cascadingTest = await registrationPage.testCascadingLocationSelection();
      
      expect(cascadingTest.provinceWorks).toBeTruthy();
      console.log('Cascading location selection results:', cascadingTest);
    });

    test('should require location selection @validation', async () => {
      const testData = {
        businessOwner: 'Test Owner',
        businessName: 'Test Business',
        address: 'Test Address',
        username: 'testuser',
        password: 'password123',
        confirmPassword: 'password123',
        workunitIds: ['1']
        // No location data
      };
      
      await registrationPage.registerBumdes(testData);
      
      // Should show location validation error
      const hasError = await registrationPage.isVisible(registrationPage.selectors.errorMessage);
      expect(hasError).toBeTruthy();
    });
  });

  test.describe('Registration Security', () => {
    test('should prevent SQL injection in registration @security', async () => {
      const maliciousData = {
        businessOwner: "'; DROP TABLE msusers; --",
        businessName: "'; DROP TABLE msusers; --",
        address: "'; DROP TABLE msusers; --",
        username: "admin'; --",
        password: "password123",
        confirmPassword: "password123",
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1']
      };
      
      await registrationPage.registerBumdes(maliciousData);
      
      // Should handle malicious input safely
      const hasError = await registrationPage.isVisible(registrationPage.selectors.errorMessage);
      console.log('SQL injection attempt handled:', hasError);
    });

    test('should sanitize input data @security', async () => {
      const scriptData = {
        businessOwner: '<script>alert("XSS")</script>',
        businessName: '<script>alert("XSS")</script>',
        address: '<script>alert("XSS")</script>',
        username: 'testuser',
        password: 'password123',
        confirmPassword: 'password123',
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1']
      };
      
      await registrationPage.registerBumdes(scriptData);
      
      // Should handle script tags safely
      const pageContent = await registrationPage.page.content();
      expect(pageContent).not.toContain('<script>alert("XSS")</script>');
    });
  });

  test.describe('Registration Performance', () => {
    test('should load registration page within acceptable time @performance', async ({ page }) => {
      const startTime = Date.now();
      await registrationPage.goto();
      await registrationPage.verifyElementVisible(registrationPage.selectors.registrationForm);
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(10000); // 10 seconds threshold
      console.log('Registration page load time:', loadTime, 'ms');
    });

    test('should complete registration within acceptable time @performance', async () => {
      const testData = {
        businessOwner: TestHelpers.generateTestData().business.ownerName,
        businessName: TestHelpers.generateTestData().business.businessName,
        address: TestHelpers.generateTestData().business.address,
        username: TestHelpers.generateRandomString(8).toLowerCase(),
        password: 'password123',
        confirmPassword: 'password123',
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1',
        workunitIds: ['1']
      };
      
      const startTime = Date.now();
      await registrationPage.registerBumdes(testData);
      const registrationTime = Date.now() - startTime;
      
      expect(registrationTime).toBeLessThan(15000); // 15 seconds threshold
      console.log('Registration completion time:', registrationTime, 'ms');
    });
  });
});
