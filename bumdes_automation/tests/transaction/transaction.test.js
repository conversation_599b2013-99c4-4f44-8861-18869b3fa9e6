const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const TransactionPage = require('../../pages/TransactionPage');
const TestHelpers = require('../../utils/helpers');

test.describe('Transaction Management Tests', () => {
  let loginPage;
  let transactionPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    transactionPage = new TransactionPage(page);
    
    // Login as BUMDes for transaction tests
    await loginPage.goto();
    await loginPage.loginAsBumdes();
    await transactionPage.goto();
    await transactionPage.verifyTransactionPageLoaded();
  });

  test.describe('Transaction CRUD Operations', () => {
    test('should create new income transaction successfully @smoke', async () => {
      const testData = TestHelpers.generateTestData().transaction;
      testData.type = 'Pendapatan';
      testData.workunitId = '1';
      
      await transactionPage.createTransaction(testData);
      
      // Verify transaction was created
      const isCreated = await transactionPage.verifyTransactionInTable(testData);
      expect(isCreated).toBeTruthy();
    });

    test('should create new expense transaction successfully @smoke', async () => {
      const testData = TestHelpers.generateTestData().transaction;
      testData.type = 'Pengeluaran';
      testData.workunitId = '1';
      
      await transactionPage.createTransaction(testData);
      
      // Verify transaction was created
      const isCreated = await transactionPage.verifyTransactionInTable(testData);
      expect(isCreated).toBeTruthy();
    });

    test('should edit transaction successfully @regression', async () => {
      // First create a transaction
      const testData = TestHelpers.generateTestData().transaction;
      testData.workunitId = '1';
      
      await transactionPage.createTransaction(testData);
      
      // Then edit it
      const updatedData = { ...testData, amount: testData.amount + 1000 };
      await transactionPage.editTransaction(0, updatedData);
      
      // Verify transaction was updated
      const isUpdated = await transactionPage.verifyTransactionInTable(updatedData);
      expect(isUpdated).toBeTruthy();
    });

    test('should delete transaction successfully @regression', async () => {
      // First create a transaction
      const testData = TestHelpers.generateTestData().transaction;
      testData.workunitId = '1';
      
      await transactionPage.createTransaction(testData);
      
      // Get initial count
      const initialCount = await transactionPage.getTransactionCount();
      
      // Delete the transaction
      await transactionPage.deleteTransaction(0);
      
      // Verify count decreased
      const finalCount = await transactionPage.getTransactionCount();
      expect(finalCount).toBeLessThan(initialCount);
    });

    test('should perform complete CRUD operations @regression', async () => {
      const testData = TestHelpers.generateTestData().transaction;
      testData.workunitId = '1';
      
      const crudResults = await transactionPage.testCRUDOperations(testData);
      
      expect(crudResults.create).toBeTruthy();
      expect(crudResults.read).toBeTruthy();
      expect(crudResults.update).toBeTruthy();
      expect(crudResults.delete).toBeTruthy();
    });
  });

  test.describe('Transaction Form Validation', () => {
    test('should validate required fields @validation', async () => {
      const hasValidation = await transactionPage.testFormValidation();
      expect(hasValidation).toBeTruthy();
    });

    test('should validate amount field @validation', async () => {
      const amountValidation = await transactionPage.testAmountValidation();
      
      // All invalid amounts should be rejected
      for (const result of amountValidation) {
        expect(result.rejected).toBeTruthy();
      }
    });

    test('should require beginning balance before creating transactions @validation', async () => {
      const balanceRequirement = await transactionPage.testBeginningBalanceRequirement();
      
      if (balanceRequirement.requiresBeginningBalance) {
        expect(balanceRequirement.errorMessage).toBeTruthy();
        console.log('Beginning balance required:', balanceRequirement.errorMessage);
      }
    });
  });

  test.describe('Modal/HPP Functionality', () => {
    test('should show modal/HPP field for income transactions @feature', async () => {
      const hppTest = await transactionPage.testModalHppFunctionality();
      
      if (hppTest.modalHppFieldVisible) {
        expect(hppTest.modalHppFieldVisible).toBeTruthy();
        console.log('Modal/HPP functionality working:', hppTest);
      }
    });

    test('should auto-create expense transaction when HPP is filled @feature', async () => {
      const testData = {
        workunitId: '1',
        type: 'Pendapatan',
        amount: 1000000,
        description: 'Test income with HPP',
        modalHpp: 600000
      };
      
      await transactionPage.createTransaction(testData);
      
      // Check if expense transaction was auto-created
      const transactionCount = await transactionPage.getTransactionCount();
      expect(transactionCount).toBeGreaterThan(0);
    });
  });

  test.describe('Transaction Filters', () => {
    test('should filter transactions by date range @filter', async () => {
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
      
      await transactionPage.applyDateFilter(yesterday, today);
      
      // Should show filtered results
      const count = await transactionPage.getTransactionCount();
      console.log('Filtered transaction count:', count);
    });

    test('should filter transactions by type @filter', async () => {
      await transactionPage.applyTypeFilter('Pendapatan');
      
      // Verify only income transactions are shown
      const count = await transactionPage.getTransactionCount();
      console.log('Income transactions count:', count);
    });

    test('should filter transactions by work unit @filter', async () => {
      await transactionPage.applyWorkunitFilter('1');
      
      // Should show filtered results
      const count = await transactionPage.getTransactionCount();
      console.log('Work unit filtered count:', count);
    });
  });

  test.describe('Transaction Search', () => {
    test('should search transactions by transaction code @search', async () => {
      // Create a transaction first
      const testData = TestHelpers.generateTestData().transaction;
      testData.workunitId = '1';
      
      await transactionPage.createTransaction(testData);
      
      // Get the first transaction data
      const transactionData = await transactionPage.getTransactionData(0);
      
      if (transactionData && transactionData.code) {
        const found = await transactionPage.searchTransactionByCode(transactionData.code);
        expect(found).toBeTruthy();
      }
    });

    test('should handle search with no results @search', async () => {
      const found = await transactionPage.searchTransactionByCode('NONEXISTENT123');
      expect(found).toBeFalsy();
    });
  });

  test.describe('Transaction Export', () => {
    test('should export transactions successfully @export', async () => {
      const exported = await transactionPage.exportTransactions();
      
      if (exported) {
        expect(exported).toBeTruthy();
        console.log('Transaction export successful');
      } else {
        console.log('Export feature not available or not visible');
      }
    });
  });

  test.describe('Transaction Pagination', () => {
    test('should navigate through transaction pages @pagination', async () => {
      const paginationTest = await transactionPage.testPagination();
      
      if (paginationTest.paginationWorks) {
        expect(paginationTest.paginationWorks).toBeTruthy();
        console.log('Pagination test results:', paginationTest);
      } else {
        console.log('Pagination not available (single page of data)');
      }
    });
  });

  test.describe('Transaction Status', () => {
    test('should display transaction status correctly @status', async () => {
      const count = await transactionPage.getTransactionCount();
      
      if (count > 0) {
        const transactionData = await transactionPage.getTransactionData(0);
        expect(transactionData.status).toBeTruthy();
        console.log('Transaction status:', transactionData.status);
      }
    });

    test('should handle status changes if applicable @status', async () => {
      const count = await transactionPage.getTransactionCount();
      
      if (count > 0) {
        // Check if status can be changed (depends on user role and transaction state)
        const statusChanged = await transactionPage.verifyStatusChange(0, 'Disetujui');
        console.log('Status change capability:', statusChanged);
      }
    });
  });

  test.describe('Access Control', () => {
    test('should show only user-accessible transactions for BUMDes User @access-control', async ({ page }) => {
      // Login as BUMDes User
      const userLoginPage = new LoginPage(page);
      const userTransactionPage = new TransactionPage(page);
      
      await userLoginPage.goto();
      await userLoginPage.loginAsBumdesUser();
      await userTransactionPage.goto();
      await userTransactionPage.verifyTransactionPageLoaded();
      
      // Should only see transactions from their work unit
      const count = await userTransactionPage.getTransactionCount();
      console.log('BUMDes User transaction count:', count);
      
      // Verify work unit consistency if there are transactions
      if (count > 0) {
        const firstTransaction = await userTransactionPage.getTransactionData(0);
        console.log('First transaction work unit:', firstTransaction.workunit);
      }
    });
  });

  test.describe('Transaction Performance', () => {
    test('should load transaction page within acceptable time @performance', async ({ page }) => {
      const startTime = Date.now();
      await transactionPage.goto();
      await transactionPage.verifyTransactionPageLoaded();
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(10000); // 10 seconds threshold
      console.log('Transaction page load time:', loadTime, 'ms');
    });

    test('should handle large transaction datasets efficiently @performance', async () => {
      const count = await transactionPage.getTransactionCount();
      
      if (count > 50) {
        // Test pagination performance with large datasets
        const paginationTest = await transactionPage.testPagination();
        expect(paginationTest.paginationWorks).toBeTruthy();
      }
      
      console.log('Transaction dataset size:', count);
    });
  });

  test.describe('Transaction Error Handling', () => {
    test('should handle form submission errors gracefully @error-handling', async () => {
      // Try to create transaction with invalid data
      const invalidData = {
        workunitId: '',
        type: '',
        amount: -1000,
        description: ''
      };
      
      await transactionPage.clickAddTransaction();
      await transactionPage.fillTransactionForm(invalidData);
      await transactionPage.submitTransaction();
      
      // Should show validation errors
      const hasError = await transactionPage.isVisible('.alert-danger') || 
                      await transactionPage.isVisible('.invalid-feedback');
      expect(hasError).toBeTruthy();
    });
  });
});
