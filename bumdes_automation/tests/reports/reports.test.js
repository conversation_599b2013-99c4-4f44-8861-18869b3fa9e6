const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const BasePage = require('../../pages/BasePage');
const TestHelpers = require('../../utils/helpers');

class ReportsPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      // Report filters
      periodSelect: 'select[name="period"]',
      yearSelect: 'select[name="year"]',
      bumdesSelect: 'select[name="bumdes_id"]',
      
      // Buttons
      generateReportBtn: '.btn-generate-report',
      exportPdfBtn: '.btn-export-pdf',
      exportExcelBtn: '.btn-export-excel',
      
      // Report content
      reportTable: '.report-table',
      reportSummary: '.report-summary',
      reportChart: '.report-chart',
      
      // Report sections
      modalSection: '.modal-section',
      omsetSection: '.omset-section',
      labaSection: '.laba-section'
    };
  }
  
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.reports);
  }
  
  async generateReport(period, year, bumdesId = null) {
    if (period) {
      await this.selectOption(this.selectors.periodSelect, period);
    }
    if (year) {
      await this.selectOption(this.selectors.yearSelect, year);
    }
    if (bumdesId && await this.isVisible(this.selectors.bumdesSelect)) {
      await this.selectOption(this.selectors.bumdesSelect, bumdesId);
    }
    
    await this.click(this.selectors.generateReportBtn);
    await this.page.waitForTimeout(3000); // Wait for report generation
  }
  
  async exportToPdf() {
    if (await this.isVisible(this.selectors.exportPdfBtn)) {
      await this.click(this.selectors.exportPdfBtn);
      await this.page.waitForTimeout(3000); // Wait for download
      return true;
    }
    return false;
  }
  
  async verifyReportContent() {
    const hasTable = await this.isVisible(this.selectors.reportTable);
    const hasSummary = await this.isVisible(this.selectors.reportSummary);
    
    return { hasTable, hasSummary };
  }
}

test.describe('Reports Tests', () => {
  let loginPage;
  let reportsPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    reportsPage = new ReportsPage(page);
  });

  test.describe('Report Generation - Super Admin', () => {
    test.beforeEach(async () => {
      await loginPage.goto();
      await loginPage.loginAsSuperAdmin();
      await reportsPage.goto();
    });

    test('should generate annual report successfully @smoke', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await reportsPage.generateReport('tahunan', currentYear);
      
      const reportContent = await reportsPage.verifyReportContent();
      expect(reportContent.hasTable || reportContent.hasSummary).toBeTruthy();
    });

    test('should generate report for specific BUMDes @regression', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await reportsPage.generateReport('tahunan', currentYear, '1');
      
      const reportContent = await reportsPage.verifyReportContent();
      expect(reportContent.hasTable || reportContent.hasSummary).toBeTruthy();
    });

    test('should export report to PDF @export', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await reportsPage.generateReport('tahunan', currentYear);
      
      const exported = await reportsPage.exportToPdf();
      if (exported) {
        expect(exported).toBeTruthy();
        console.log('PDF export successful');
      }
    });
  });

  test.describe('Report Generation - BUMDes', () => {
    test.beforeEach(async () => {
      await loginPage.goto();
      await loginPage.loginAsBumdes();
      await reportsPage.goto();
    });

    test('should generate BUMDes specific report @smoke', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await reportsPage.generateReport('tahunan', currentYear);
      
      const reportContent = await reportsPage.verifyReportContent();
      expect(reportContent.hasTable || reportContent.hasSummary).toBeTruthy();
    });

    test('should show only own BUMDes data @access-control', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await reportsPage.generateReport('tahunan', currentYear);
      
      // BUMDes should not see BUMDes selection dropdown
      const hasBumdesSelect = await reportsPage.isVisible(reportsPage.selectors.bumdesSelect);
      expect(hasBumdesSelect).toBeFalsy();
    });
  });

  test.describe('Report Data Validation', () => {
    test.beforeEach(async () => {
      await loginPage.goto();
      await loginPage.loginAsBumdes();
      await reportsPage.goto();
    });

    test('should display correct financial calculations @data-integrity', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await reportsPage.generateReport('tahunan', currentYear);
      
      // Verify report sections are present
      const hasModal = await reportsPage.isVisible(reportsPage.selectors.modalSection);
      const hasOmset = await reportsPage.isVisible(reportsPage.selectors.omsetSection);
      const hasLaba = await reportsPage.isVisible(reportsPage.selectors.labaSection);
      
      console.log('Report sections:', { modal: hasModal, omset: hasOmset, laba: hasLaba });
    });

    test('should handle empty data gracefully @error-handling', async () => {
      // Generate report for future year (should have no data)
      const futureYear = (new Date().getFullYear() + 1).toString();
      
      await reportsPage.generateReport('tahunan', futureYear);
      
      // Should still show report structure even with no data
      const reportContent = await reportsPage.verifyReportContent();
      console.log('Empty data report:', reportContent);
    });
  });

  test.describe('Report Performance', () => {
    test.beforeEach(async () => {
      await loginPage.goto();
      await loginPage.loginAsSuperAdmin();
      await reportsPage.goto();
    });

    test('should generate report within acceptable time @performance', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      const startTime = Date.now();
      await reportsPage.generateReport('tahunan', currentYear);
      const generationTime = Date.now() - startTime;
      
      expect(generationTime).toBeLessThan(30000); // 30 seconds threshold
      console.log('Report generation time:', generationTime, 'ms');
    });
  });
});
