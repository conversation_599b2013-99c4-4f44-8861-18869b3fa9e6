const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const BasePage = require('../../pages/BasePage');
const TestHelpers = require('../../utils/helpers');

class MasterDataPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      // BUMDes Management
      bumdesTable: '.bumdes-table',
      addBumdesBtn: '.btn-add-bumdes',
      bumdesForm: '#frmBumdes',
      bumdesNameInput: 'input[name="bumdes_name"]',
      
      // Work Units Management
      workunitTable: '.workunit-table',
      addWorkunitBtn: '.btn-add-workunit',
      workunitForm: '#frmWorkunit',
      workunitNameInput: 'input[name="workunit_name"]',
      
      // Location selectors
      provinceSelect: 'select[name="province_id"]',
      citySelect: 'select[name="city_id"]',
      districtSelect: 'select[name="district_id"]',
      villageSelect: 'select[name="village_id"]'
    };
  }
  
  async gotoBumdes() {
    await super.goto(this.config.baseURL + this.config.urls.bumdes);
  }
  
  async gotoWorkunits() {
    await super.goto(this.config.baseURL + this.config.urls.workunits);
  }
  
  async createBumdes(bumdesData) {
    await this.click(this.selectors.addBumdesBtn);
    await this.waitForElement(this.selectors.bumdesForm);
    
    if (bumdesData.name) {
      await this.fill(this.selectors.bumdesNameInput, bumdesData.name);
    }
    
    // Fill location data if provided
    if (bumdesData.provinceId) {
      await this.selectOption(this.selectors.provinceSelect, bumdesData.provinceId);
      await this.page.waitForTimeout(1000); // Wait for cascade
    }
    
    if (bumdesData.cityId) {
      await this.selectOption(this.selectors.citySelect, bumdesData.cityId);
      await this.page.waitForTimeout(1000);
    }
    
    if (bumdesData.districtId) {
      await this.selectOption(this.selectors.districtSelect, bumdesData.districtId);
      await this.page.waitForTimeout(1000);
    }
    
    if (bumdesData.villageId) {
      await this.selectOption(this.selectors.villageSelect, bumdesData.villageId);
    }
    
    await this.submitForm();
  }
  
  async createWorkunit(workunitData) {
    await this.click(this.selectors.addWorkunitBtn);
    await this.waitForElement(this.selectors.workunitForm);
    
    if (workunitData.name) {
      await this.fill(this.selectors.workunitNameInput, workunitData.name);
    }
    
    await this.submitForm();
  }
  
  async verifyBumdesInTable(bumdesName) {
    const tableRows = await this.page.locator(this.selectors.bumdesTable + ' tbody tr').count();
    
    for (let i = 0; i < tableRows; i++) {
      const rowText = await this.page.locator(`${this.selectors.bumdesTable} tbody tr:nth-child(${i + 1})`).textContent();
      if (rowText.includes(bumdesName)) {
        return true;
      }
    }
    return false;
  }
  
  async verifyWorkunitInTable(workunitName) {
    const tableRows = await this.page.locator(this.selectors.workunitTable + ' tbody tr').count();
    
    for (let i = 0; i < tableRows; i++) {
      const rowText = await this.page.locator(`${this.selectors.workunitTable} tbody tr:nth-child(${i + 1})`).textContent();
      if (rowText.includes(workunitName)) {
        return true;
      }
    }
    return false;
  }
}

test.describe('Master Data Management Tests', () => {
  let loginPage;
  let masterDataPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    masterDataPage = new MasterDataPage(page);
    
    // Login as Super Admin for master data tests
    await loginPage.goto();
    await loginPage.loginAsSuperAdmin();
  });

  test.describe('BUMDes Master Data', () => {
    test.beforeEach(async () => {
      await masterDataPage.gotoBumdes();
    });

    test('should display BUMDes list correctly @smoke', async () => {
      await masterDataPage.verifyElementVisible(masterDataPage.selectors.bumdesTable);
      await masterDataPage.verifyElementVisible(masterDataPage.selectors.addBumdesBtn);
    });

    test('should create new BUMDes successfully @regression', async () => {
      const testData = {
        name: 'BUMDes Test ' + TestHelpers.generateRandomString(5),
        provinceId: '1',
        cityId: '1',
        districtId: '1',
        villageId: '1'
      };
      
      await masterDataPage.createBumdes(testData);
      
      // Verify BUMDes was created
      const isCreated = await masterDataPage.verifyBumdesInTable(testData.name);
      expect(isCreated).toBeTruthy();
    });

    test('should validate required fields for BUMDes @validation', async () => {
      await masterDataPage.click(masterDataPage.selectors.addBumdesBtn);
      await masterDataPage.waitForElement(masterDataPage.selectors.bumdesForm);
      
      // Try to submit empty form
      await masterDataPage.submitForm();
      
      // Should show validation errors
      const hasError = await masterDataPage.isVisible('.alert-danger') || 
                      await masterDataPage.isVisible('.invalid-feedback');
      expect(hasError).toBeTruthy();
    });

    test('should test cascading location selection @functionality', async () => {
      await masterDataPage.click(masterDataPage.selectors.addBumdesBtn);
      await masterDataPage.waitForElement(masterDataPage.selectors.bumdesForm);
      
      // Select province
      await masterDataPage.selectOption(masterDataPage.selectors.provinceSelect, '1');
      await masterDataPage.page.waitForTimeout(2000);
      
      // City dropdown should be populated
      const cityOptions = await masterDataPage.page.locator(masterDataPage.selectors.citySelect + ' option').count();
      expect(cityOptions).toBeGreaterThan(1); // Should have more than just the default option
      
      // Select city
      await masterDataPage.selectOption(masterDataPage.selectors.citySelect, '1');
      await masterDataPage.page.waitForTimeout(2000);
      
      // District dropdown should be populated
      const districtOptions = await masterDataPage.page.locator(masterDataPage.selectors.districtSelect + ' option').count();
      expect(districtOptions).toBeGreaterThan(1);
    });
  });

  test.describe('Work Units Master Data', () => {
    test.beforeEach(async () => {
      await masterDataPage.gotoWorkunits();
    });

    test('should display work units list correctly @smoke', async () => {
      await masterDataPage.verifyElementVisible(masterDataPage.selectors.workunitTable);
      await masterDataPage.verifyElementVisible(masterDataPage.selectors.addWorkunitBtn);
    });

    test('should create new work unit successfully @regression', async () => {
      const testData = {
        name: 'Unit Usaha Test ' + TestHelpers.generateRandomString(5)
      };
      
      await masterDataPage.createWorkunit(testData);
      
      // Verify work unit was created
      const isCreated = await masterDataPage.verifyWorkunitInTable(testData.name);
      expect(isCreated).toBeTruthy();
    });

    test('should validate required fields for work unit @validation', async () => {
      await masterDataPage.click(masterDataPage.selectors.addWorkunitBtn);
      await masterDataPage.waitForElement(masterDataPage.selectors.workunitForm);
      
      // Try to submit empty form
      await masterDataPage.submitForm();
      
      // Should show validation errors
      const hasError = await masterDataPage.isVisible('.alert-danger') || 
                      await masterDataPage.isVisible('.invalid-feedback');
      expect(hasError).toBeTruthy();
    });

    test('should prevent duplicate work unit names @validation', async () => {
      const testData = {
        name: 'Duplicate Test Unit'
      };
      
      // Create first work unit
      await masterDataPage.createWorkunit(testData);
      
      // Try to create duplicate
      await masterDataPage.createWorkunit(testData);
      
      // Should show error for duplicate
      const hasError = await masterDataPage.isVisible('.alert-danger');
      if (hasError) {
        const errorText = await masterDataPage.getText('.alert-danger');
        expect(errorText.toLowerCase()).toContain('sudah ada');
      }
    });
  });

  test.describe('Master Data Access Control', () => {
    test('should restrict master data access to Super Admin only @access-control', async ({ page }) => {
      // Try to access as BUMDes user
      const bumdesLoginPage = new LoginPage(page);
      const bumdesMasterPage = new MasterDataPage(page);
      
      await bumdesLoginPage.goto();
      await bumdesLoginPage.loginAsBumdes();
      
      // Try to access BUMDes master data
      await bumdesMasterPage.gotoBumdes();
      
      // Should be redirected or show access denied
      const currentURL = await page.url();
      const hasAccessDenied = currentURL.includes('403') || 
                            currentURL.includes('access-denied') ||
                            await bumdesMasterPage.isVisible('.access-denied');
      
      if (!hasAccessDenied) {
        // If not redirected, check if user can see the page but not perform actions
        const canAddBumdes = await bumdesMasterPage.isVisible(bumdesMasterPage.selectors.addBumdesBtn);
        expect(canAddBumdes).toBeFalsy();
      }
    });
  });

  test.describe('Master Data Performance', () => {
    test('should load master data pages within acceptable time @performance', async () => {
      // Test BUMDes page load time
      const startTime = Date.now();
      await masterDataPage.gotoBumdes();
      await masterDataPage.waitForTable();
      const bumdesLoadTime = Date.now() - startTime;
      
      expect(bumdesLoadTime).toBeLessThan(10000); // 10 seconds threshold
      console.log('BUMDes page load time:', bumdesLoadTime, 'ms');
      
      // Test Work units page load time
      const startTime2 = Date.now();
      await masterDataPage.gotoWorkunits();
      await masterDataPage.waitForTable();
      const workunitLoadTime = Date.now() - startTime2;
      
      expect(workunitLoadTime).toBeLessThan(10000);
      console.log('Work units page load time:', workunitLoadTime, 'ms');
    });
  });

  test.describe('Master Data Error Handling', () => {
    test('should handle form submission errors gracefully @error-handling', async () => {
      await masterDataPage.gotoBumdes();
      
      // Try to create BUMDes with invalid data
      const invalidData = {
        name: '', // Empty name
        provinceId: 'invalid',
        cityId: 'invalid'
      };
      
      await masterDataPage.click(masterDataPage.selectors.addBumdesBtn);
      await masterDataPage.waitForElement(masterDataPage.selectors.bumdesForm);
      
      // Fill invalid data
      await masterDataPage.fill(masterDataPage.selectors.bumdesNameInput, invalidData.name);
      
      await masterDataPage.submitForm();
      
      // Should show validation errors
      const hasError = await masterDataPage.isVisible('.alert-danger') || 
                      await masterDataPage.isVisible('.invalid-feedback');
      expect(hasError).toBeTruthy();
    });

    test('should handle network errors during data operations @error-handling', async ({ page }) => {
      await masterDataPage.gotoBumdes();
      
      const testData = {
        name: 'Network Test BUMDes'
      };
      
      await masterDataPage.click(masterDataPage.selectors.addBumdesBtn);
      await masterDataPage.waitForElement(masterDataPage.selectors.bumdesForm);
      await masterDataPage.fill(masterDataPage.selectors.bumdesNameInput, testData.name);
      
      // Simulate network failure during submission
      await page.route('**/master/**', route => route.abort());
      
      try {
        await masterDataPage.submitForm();
      } catch (error) {
        console.log('Network error handled during master data operation');
      }
      
      // Restore network
      await page.unroute('**/master/**');
    });
  });
});
