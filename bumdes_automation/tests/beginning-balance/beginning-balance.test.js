const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const BasePage = require('../../pages/BasePage');
const TestHelpers = require('../../utils/helpers');

class BeginningBalancePage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      // Balance form
      balanceForm: '#frmBeginningBalance',
      periodInput: 'input[name="period"]',
      workunitSelect: 'select[name="workunit_id"]',
      amountInput: 'input[name="amount"]',
      descriptionInput: 'textarea[name="description"]',
      
      // Buttons
      addBalanceBtn: '.btn-add-balance',
      submitBtn: 'button[type="submit"]',
      addAmountBtn: '.btn-add-amount',
      subtractAmountBtn: '.btn-subtract-amount',
      historyBtn: '.btn-history',
      
      // Table
      balanceTable: '.datatables',
      tableRows: 'tbody tr',
      
      // Modal
      balanceModal: '#balanceModal',
      addAmountModal: '#addAmountModal',
      subtractAmountModal: '#subtractAmountModal',
      historyModal: '#historyModal',
      
      // History table
      historyTable: '.history-table',
      
      // Filters
      periodFilter: 'select[name="period_filter"]',
      workunitFilter: 'select[name="workunit_filter"]',
      filterBtn: '.btn-filter'
    };
  }
  
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.beginningBalance);
  }
  
  async createBeginningBalance(balanceData) {
    await this.click(this.selectors.addBalanceBtn);
    await this.waitForElement(this.selectors.balanceForm);
    
    // Fill period
    if (balanceData.period) {
      await this.fill(this.selectors.periodInput, balanceData.period);
    }
    
    // Select work unit
    if (balanceData.workunitId) {
      await this.selectOption(this.selectors.workunitSelect, balanceData.workunitId);
    }
    
    // Fill amount
    if (balanceData.amount) {
      await this.fill(this.selectors.amountInput, balanceData.amount.toString());
    }
    
    // Fill description
    if (balanceData.description) {
      await this.fill(this.selectors.descriptionInput, balanceData.description);
    }
    
    await this.submitForm();
  }
  
  async addAmount(rowIndex, amount, description = '') {
    const addBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.addAmountBtn}`;
    await this.click(addBtnSelector);
    await this.waitForElement(this.selectors.addAmountModal);
    
    await this.fill('input[name="adjustment_amount"]', amount.toString());
    
    if (description) {
      await this.fill('textarea[name="adjustment_description"]', description);
    }
    
    await this.click('button.btn-confirm-add');
    await this.helpers.waitForPageLoad(this.page);
  }
  
  async subtractAmount(rowIndex, amount, description = '') {
    const subtractBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.subtractAmountBtn}`;
    await this.click(subtractBtnSelector);
    await this.waitForElement(this.selectors.subtractAmountModal);
    
    await this.fill('input[name="adjustment_amount"]', amount.toString());
    
    if (description) {
      await this.fill('textarea[name="adjustment_description"]', description);
    }
    
    await this.click('button.btn-confirm-subtract');
    await this.helpers.waitForPageLoad(this.page);
  }
  
  async viewHistory(rowIndex) {
    const historyBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.historyBtn}`;
    await this.click(historyBtnSelector);
    await this.waitForElement(this.selectors.historyModal);
    
    // Get history entries count
    const historyCount = await this.page.locator(`${this.selectors.historyTable} tbody tr`).count();
    
    return historyCount;
  }
  
  async getBalanceCount() {
    return await this.getTableRowCount();
  }
  
  async getBalanceData(rowIndex) {
    const row = `${this.selectors.tableRows}:nth-child(${rowIndex + 1})`;
    
    if (await this.isVisible(row)) {
      const cells = await this.page.locator(`${row} td`).allTextContents();
      return {
        period: cells[1] || '',
        workunit: cells[2] || '',
        amount: cells[3] || '',
        description: cells[4] || ''
      };
    }
    return null;
  }
  
  async verifyBalanceInTable(balanceData) {
    const count = await this.getBalanceCount();
    
    for (let i = 0; i < count; i++) {
      const rowData = await this.getBalanceData(i);
      
      if (rowData && 
          rowData.period === balanceData.period &&
          rowData.amount.includes(balanceData.amount.toString())) {
        return true;
      }
    }
    return false;
  }
  
  async applyPeriodFilter(period) {
    await this.selectOption(this.selectors.periodFilter, period);
    await this.click(this.selectors.filterBtn);
    await this.page.waitForTimeout(2000);
  }
  
  async applyWorkunitFilter(workunitId) {
    await this.selectOption(this.selectors.workunitFilter, workunitId);
    await this.click(this.selectors.filterBtn);
    await this.page.waitForTimeout(2000);
  }
  
  async testFormValidation() {
    await this.click(this.selectors.addBalanceBtn);
    
    // Try to submit empty form
    await this.submitForm();
    
    // Check for validation messages
    const hasValidationErrors = await this.isVisible('.invalid-feedback') || 
                               await this.isVisible('.alert-danger');
    
    return hasValidationErrors;
  }
  
  async testDuplicatePeriod(period, workunitId) {
    // Create first balance
    const testData = {
      period: period,
      workunitId: workunitId,
      amount: 1000000,
      description: 'Test balance'
    };
    
    await this.createBeginningBalance(testData);
    
    // Try to create duplicate
    await this.createBeginningBalance(testData);
    
    // Check for duplicate error
    const hasError = await this.isVisible('.alert-danger');
    
    if (hasError) {
      const errorText = await this.getText('.alert-danger');
      return errorText.includes('sudah ada') || errorText.includes('duplicate');
    }
    
    return false;
  }
}

test.describe('Beginning Balance Tests', () => {
  let loginPage;
  let beginningBalancePage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    beginningBalancePage = new BeginningBalancePage(page);
    
    // Login as BUMDes for beginning balance tests
    await loginPage.goto();
    await loginPage.loginAsBumdes();
    await beginningBalancePage.goto();
  });

  test.describe('Beginning Balance CRUD Operations', () => {
    test('should create new beginning balance successfully @smoke', async () => {
      const currentYear = new Date().getFullYear().toString();
      const testData = {
        period: currentYear,
        workunitId: '1',
        amount: 5000000,
        description: 'Initial balance for testing'
      };
      
      await beginningBalancePage.createBeginningBalance(testData);
      
      // Verify balance was created
      const isCreated = await beginningBalancePage.verifyBalanceInTable(testData);
      expect(isCreated).toBeTruthy();
    });

    test('should add amount to existing balance @regression', async () => {
      // First get initial balance
      const initialBalanceData = await beginningBalancePage.getBalanceData(0);
      
      if (initialBalanceData) {
        const initialAmount = parseFloat(initialBalanceData.amount.replace(/[^\d.-]/g, ''));
        const addAmount = 1000000;
        
        // Add amount
        await beginningBalancePage.addAmount(0, addAmount, 'Adding test amount');
        
        // Get updated balance
        const updatedBalanceData = await beginningBalancePage.getBalanceData(0);
        const updatedAmount = parseFloat(updatedBalanceData.amount.replace(/[^\d.-]/g, ''));
        
        // Verify amount increased
        expect(updatedAmount).toBeGreaterThan(initialAmount);
        console.log('Balance adjustment:', { initial: initialAmount, updated: updatedAmount });
      }
    });

    test('should subtract amount from existing balance @regression', async () => {
      // First get initial balance
      const initialBalanceData = await beginningBalancePage.getBalanceData(0);
      
      if (initialBalanceData) {
        const initialAmount = parseFloat(initialBalanceData.amount.replace(/[^\d.-]/g, ''));
        const subtractAmount = 500000;
        
        // Subtract amount
        await beginningBalancePage.subtractAmount(0, subtractAmount, 'Subtracting test amount');
        
        // Get updated balance
        const updatedBalanceData = await beginningBalancePage.getBalanceData(0);
        const updatedAmount = parseFloat(updatedBalanceData.amount.replace(/[^\d.-]/g, ''));
        
        // Verify amount decreased
        expect(updatedAmount).toBeLessThan(initialAmount);
        console.log('Balance adjustment:', { initial: initialAmount, updated: updatedAmount });
      }
    });

    test('should view balance history @regression', async () => {
      // View history
      const historyCount = await beginningBalancePage.viewHistory(0);
      
      // Should have at least one history entry
      expect(historyCount).toBeGreaterThan(0);
      console.log('Balance history entries:', historyCount);
    });
  });

  test.describe('Beginning Balance Validation', () => {
    test('should validate required fields @validation', async () => {
      const hasValidation = await beginningBalancePage.testFormValidation();
      expect(hasValidation).toBeTruthy();
    });

    test('should prevent duplicate period for same work unit @validation', async () => {
      const currentYear = new Date().getFullYear().toString();
      const isDuplicate = await beginningBalancePage.testDuplicatePeriod(currentYear, '1');
      expect(isDuplicate).toBeTruthy();
    });

    test('should validate amount field @validation', async () => {
      await beginningBalancePage.click(beginningBalancePage.selectors.addBalanceBtn);
      await beginningBalancePage.waitForElement(beginningBalancePage.selectors.balanceForm);
      
      // Try negative amount
      await beginningBalancePage.fill(beginningBalancePage.selectors.amountInput, '-1000');
      await beginningBalancePage.submitForm();
      
      // Should show validation error
      const hasError = await beginningBalancePage.isVisible('.invalid-feedback') || 
                      await beginningBalancePage.isVisible('.alert-danger');
      expect(hasError).toBeTruthy();
    });
  });

  test.describe('Beginning Balance Filters', () => {
    test('should filter balances by period @filter', async () => {
      const currentYear = new Date().getFullYear().toString();
      
      await beginningBalancePage.applyPeriodFilter(currentYear);
      
      // Should show filtered results
      const count = await beginningBalancePage.getBalanceCount();
      console.log('Period filtered balance count:', count);
    });

    test('should filter balances by work unit @filter', async () => {
      await beginningBalancePage.applyWorkunitFilter('1');
      
      // Should show filtered results
      const count = await beginningBalancePage.getBalanceCount();
      console.log('Work unit filtered balance count:', count);
    });
  });

  test.describe('Beginning Balance Access Control', () => {
    test('should verify BUMDes User can only see their unit balances @access-control', async ({ page }) => {
      // Login as BUMDes User
      const userLoginPage = new LoginPage(page);
      const userBalancePage = new BeginningBalancePage(page);
      
      await userLoginPage.goto();
      await userLoginPage.loginAsBumdesUser();
      await userBalancePage.goto();
      
      // Should only see balances from their work unit
      const count = await userBalancePage.getBalanceCount();
      console.log('BUMDes User balance count:', count);
      
      // Verify work unit consistency if there are balances
      if (count > 0) {
        const firstBalance = await userBalancePage.getBalanceData(0);
        console.log('First balance work unit:', firstBalance.workunit);
      }
    });
  });

  test.describe('Beginning Balance Performance', () => {
    test('should load beginning balance page within acceptable time @performance', async ({ page }) => {
      const startTime = Date.now();
      await beginningBalancePage.goto();
      await beginningBalancePage.waitForTable();
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(10000); // 10 seconds threshold
      console.log('Beginning balance page load time:', loadTime, 'ms');
    });
  });
});
