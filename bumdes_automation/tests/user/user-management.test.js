const { test, expect } = require('@playwright/test');
const LoginPage = require('../../pages/LoginPage');
const UserManagementPage = require('../../pages/UserManagementPage');
const TestHelpers = require('../../utils/helpers');

test.describe('User Management Tests', () => {
  let loginPage;
  let userManagementPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    userManagementPage = new UserManagementPage(page);
    
    // Login as BUMDes for user management tests
    await loginPage.goto();
    await loginPage.loginAsBumdes();
    await userManagementPage.goto();
    await userManagementPage.verifyUserManagementPageLoaded();
  });

  test.describe('User CRUD Operations', () => {
    test('should create new user successfully @smoke', async () => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Verify user was created
      const isCreated = await userManagementPage.verifyUserInTable(testData);
      expect(isCreated).toBeTruthy();
    });

    test('should edit user successfully @regression', async () => {
      // First create a user
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Then edit it
      const updatedData = { ...testData, name: testData.name + ' Updated' };
      await userManagementPage.editUser(0, updatedData);
      
      // Verify user was updated
      const isUpdated = await userManagementPage.verifyUserInTable(updatedData);
      expect(isUpdated).toBeTruthy();
    });

    test('should delete user successfully @regression', async () => {
      // First create a user
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Get initial count
      const initialCount = await userManagementPage.getUserCount();
      
      // Delete the user
      await userManagementPage.deleteUser(0);
      
      // Verify count decreased
      const finalCount = await userManagementPage.getUserCount();
      expect(finalCount).toBeLessThan(initialCount);
    });

    test('should perform complete CRUD operations @regression', async () => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      const crudResults = await userManagementPage.testCRUDOperations(testData);
      
      expect(crudResults.create).toBeTruthy();
      expect(crudResults.read).toBeTruthy();
      expect(crudResults.update).toBeTruthy();
      expect(crudResults.delete).toBeTruthy();
    });
  });

  test.describe('User Form Validation', () => {
    test('should validate required fields @validation', async () => {
      const hasValidation = await userManagementPage.testFormValidation();
      expect(hasValidation).toBeTruthy();
    });

    test('should validate username uniqueness @validation', async () => {
      // First create a user
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Try to create another user with same username
      const isDuplicate = await userManagementPage.testUsernameUniqueness(testData.username);
      expect(isDuplicate).toBeTruthy();
    });

    test('should validate password strength @validation', async () => {
      const passwordValidation = await userManagementPage.testPasswordValidation();
      
      // Weak passwords should be rejected
      for (const result of passwordValidation) {
        if (result.password.length < 6) {
          expect(result.rejected).toBeTruthy();
        }
      }
    });

    test('should validate phone number format @validation', async () => {
      const phoneValidation = await userManagementPage.testPhoneValidation();
      
      // Invalid phone numbers should be rejected
      for (const result of phoneValidation) {
        if (result.phone === 'abc123' || result.phone.length < 10) {
          expect(result.rejected).toBeTruthy();
        }
      }
    });
  });

  test.describe('User Search and Filter', () => {
    test('should search users by name @search', async () => {
      // Create a user first
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Search for the user
      const found = await userManagementPage.searchUserByName(testData.name);
      expect(found).toBeTruthy();
    });

    test('should filter users by status @filter', async () => {
      await userManagementPage.applyStatusFilter('Active');
      
      // Should show filtered results
      const count = await userManagementPage.getUserCount();
      console.log('Active users count:', count);
    });

    test('should filter users by work unit @filter', async () => {
      await userManagementPage.applyWorkunitFilter('1');
      
      // Should show filtered results
      const count = await userManagementPage.getUserCount();
      console.log('Work unit filtered users count:', count);
    });

    test('should handle search with no results @search', async () => {
      const found = await userManagementPage.searchUserByName('NONEXISTENTUSER123');
      expect(found).toBeFalsy();
    });
  });

  test.describe('User Access Control', () => {
    test('should verify user access control for BUMDes @access-control', async () => {
      const accessControl = await userManagementPage.verifyUserAccessControl();
      
      console.log('Access control verification:', accessControl);
      
      // Users should only see data from their own village/unit
      if (accessControl.userCount > 0) {
        expect(accessControl.hasAccessControl).toBeTruthy();
      }
    });

    test('should show only unit-specific users for BUMDes User @access-control', async ({ page }) => {
      // Login as BUMDes User
      const userLoginPage = new LoginPage(page);
      const userUserManagementPage = new UserManagementPage(page);
      
      await userLoginPage.goto();
      await userLoginPage.loginAsBumdesUser();
      await userUserManagementPage.goto();
      await userUserManagementPage.verifyUserManagementPageLoaded();
      
      // Should only see users from their work unit
      const count = await userUserManagementPage.getUserCount();
      console.log('BUMDes User - visible users count:', count);
      
      // Verify work unit consistency if there are users
      if (count > 0) {
        const accessControl = await userUserManagementPage.verifyUserAccessControl();
        expect(accessControl.hasAccessControl).toBeTruthy();
      }
    });
  });

  test.describe('User Data Validation', () => {
    test('should display user data correctly in table @data-integrity', async () => {
      const count = await userManagementPage.getUserCount();
      
      if (count > 0) {
        for (let i = 0; i < Math.min(count, 5); i++) {
          const userData = await userManagementPage.getUserData(i);
          
          // Verify required fields are present
          expect(userData.name).toBeTruthy();
          expect(userData.username).toBeTruthy();
          
          console.log(`User ${i + 1}:`, userData);
        }
      }
    });

    test('should maintain data consistency after operations @data-integrity', async () => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      // Create user
      await userManagementPage.createUser(testData);
      
      // Verify data integrity
      const userData = await userManagementPage.getUserData(0);
      expect(userData.name).toBe(testData.name);
      expect(userData.username).toBe(testData.username);
    });
  });

  test.describe('User Interface Tests', () => {
    test('should display user management interface correctly @ui', async () => {
      // Verify table is visible
      await userManagementPage.verifyElementVisible(userManagementPage.selectors.userTable);
      
      // Verify add user button is visible
      await userManagementPage.verifyElementVisible(userManagementPage.selectors.addUserBtn);
      
      // Verify filters are available
      if (await userManagementPage.isVisible(userManagementPage.selectors.statusFilter)) {
        await userManagementPage.verifyElementVisible(userManagementPage.selectors.statusFilter);
      }
    });

    test('should open and close user modal correctly @ui', async () => {
      await userManagementPage.clickAddUser();
      
      // Modal should be visible
      await userManagementPage.verifyElementVisible(userManagementPage.selectors.userModal);
      
      // Close modal
      await userManagementPage.closeModal();
      
      // Modal should be hidden
      await userManagementPage.verifyElementHidden(userManagementPage.selectors.userModal);
    });
  });

  test.describe('User Management Performance', () => {
    test('should load user management page within acceptable time @performance', async ({ page }) => {
      const startTime = Date.now();
      await userManagementPage.goto();
      await userManagementPage.verifyUserManagementPageLoaded();
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(10000); // 10 seconds threshold
      console.log('User management page load time:', loadTime, 'ms');
    });

    test('should handle user operations efficiently @performance', async () => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      const startTime = Date.now();
      await userManagementPage.createUser(testData);
      const operationTime = Date.now() - startTime;
      
      expect(operationTime).toBeLessThan(15000); // 15 seconds threshold
      console.log('User creation time:', operationTime, 'ms');
    });
  });

  test.describe('User Management Error Handling', () => {
    test('should handle form submission errors gracefully @error-handling', async () => {
      // Try to create user with invalid data
      const invalidData = {
        name: '',
        username: '',
        password: '123',
        phone: 'invalid',
        workunitId: ''
      };
      
      await userManagementPage.clickAddUser();
      await userManagementPage.fillUserForm(invalidData);
      await userManagementPage.submitUser();
      
      // Should show validation errors
      const hasError = await userManagementPage.isVisible('.alert-danger') || 
                      await userManagementPage.isVisible('.invalid-feedback');
      expect(hasError).toBeTruthy();
    });

    test('should handle network errors during user operations @error-handling', async ({ page }) => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.clickAddUser();
      await userManagementPage.fillUserForm(testData);
      
      // Simulate network failure during submission
      await page.route('**/bumdes_users/**', route => route.abort());
      
      try {
        await userManagementPage.submitUser();
      } catch (error) {
        console.log('Network error handled during user creation');
      }
      
      // Restore network
      await page.unroute('**/bumdes_users/**');
    });
  });

  test.describe('User Role Management', () => {
    test('should create BUMDes User with correct role @role-management', async () => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Verify user was created with correct role
      const userData = await userManagementPage.getUserData(0);
      console.log('Created user role information:', userData);
    });

    test('should assign work unit correctly @role-management', async () => {
      const testData = TestHelpers.generateTestData().user;
      testData.workunitId = '1';
      
      await userManagementPage.createUser(testData);
      
      // Verify work unit assignment
      const userData = await userManagementPage.getUserData(0);
      expect(userData.workunit).toBeTruthy();
      console.log('User work unit:', userData.workunit);
    });
  });
});
