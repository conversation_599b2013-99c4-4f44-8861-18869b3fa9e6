const { test, expect } = require('@playwright/test');
const LoginPage = require('../pages/LoginPage');
const DashboardPage = require('../pages/DashboardPage');

test.describe('SweetAlert v1 Selector Tests', () => {
  let loginPage;
  let dashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    await loginPage.goto();
  });

  test('should detect and handle SweetAlert v1 after login @smoke', async ({ page }) => {
    console.log('Testing SweetAlert v1 detection and handling...');
    
    // Fill login credentials
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    
    // Click login and wait for response
    await loginPage.clickLogin();
    
    // Wait a moment for Sweet<PERSON>lert to appear
    await page.waitForTimeout(2000);
    
    // Check for different possible SweetAlert selectors
    const possibleSelectors = [
      '.sweet-alert',
      '.swal-overlay',
      '.swal-modal',
      '.sa-confirm-button-container',
      '[class*="sweet"]',
      '[class*="swal"]'
    ];
    
    let foundSelector = null;
    let alertVisible = false;
    
    for (const selector of possibleSelectors) {
      try {
        const isVisible = await page.isVisible(selector, { timeout: 1000 });
        if (isVisible) {
          foundSelector = selector;
          alertVisible = true;
          console.log(`✅ Found SweetAlert with selector: ${selector}`);
          break;
        }
      } catch (error) {
        // Continue checking other selectors
      }
    }
    
    if (alertVisible && foundSelector) {
      console.log(`SweetAlert detected with selector: ${foundSelector}`);
      
      // Try to get alert content
      try {
        const alertElement = await page.locator(foundSelector);
        const alertText = await alertElement.textContent();
        console.log('Alert content:', alertText);
        
        // Look for confirm button within the alert
        const confirmSelectors = [
          `${foundSelector} .confirm`,
          `${foundSelector} button`,
          `${foundSelector} .sa-confirm-button-container button`,
          `${foundSelector} .swal-button--confirm`,
          `${foundSelector} [class*="confirm"]`,
          `${foundSelector} [class*="ok"]`
        ];
        
        let buttonClicked = false;
        for (const btnSelector of confirmSelectors) {
          try {
            const btnVisible = await page.isVisible(btnSelector, { timeout: 500 });
            if (btnVisible) {
              console.log(`Clicking confirm button: ${btnSelector}`);
              await page.click(btnSelector);
              buttonClicked = true;
              break;
            }
          } catch (error) {
            // Continue trying other button selectors
          }
        }
        
        if (!buttonClicked) {
          // Try clicking anywhere on the alert as fallback
          console.log('Trying to click on alert container as fallback');
          await page.click(foundSelector);
        }
        
        // Wait for alert to disappear
        await page.waitForSelector(foundSelector, { state: 'hidden', timeout: 5000 });
        console.log('✅ SweetAlert dismissed successfully');
        
      } catch (error) {
        console.log('Error handling SweetAlert:', error.message);
      }
    } else {
      console.log('ℹ️ No SweetAlert detected - checking if already redirected');
    }
    
    // Wait for potential redirect
    await page.waitForTimeout(3000);
    
    // Check final URL
    const currentURL = page.url();
    console.log('Final URL:', currentURL);
    
    if (currentURL.includes('dashboard')) {
      console.log('✅ Successfully redirected to dashboard');
      await dashboardPage.verifyDashboardLoaded();
    } else {
      console.log('⚠️ Not redirected to dashboard, current URL:', currentURL);
    }
  });

  test('should inspect page for SweetAlert elements after login @debug', async ({ page }) => {
    console.log('Inspecting page for SweetAlert elements...');
    
    // Perform login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for any alerts to appear
    await page.waitForTimeout(3000);
    
    // Get all elements that might be SweetAlert related
    const allElements = await page.evaluate(() => {
      const elements = [];
      
      // Look for elements with sweet, swal, or alert in class names
      const allEls = document.querySelectorAll('*');
      allEls.forEach(el => {
        const className = el.className || '';
        const id = el.id || '';
        
        if (className.includes('sweet') || 
            className.includes('swal') || 
            className.includes('alert') ||
            id.includes('sweet') ||
            id.includes('swal') ||
            id.includes('alert')) {
          elements.push({
            tagName: el.tagName,
            className: className,
            id: id,
            textContent: el.textContent?.substring(0, 100),
            isVisible: el.offsetParent !== null
          });
        }
      });
      
      return elements;
    });
    
    console.log('Found potential SweetAlert elements:');
    allElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName} - Class: "${el.className}" - ID: "${el.id}" - Visible: ${el.isVisible}`);
      if (el.textContent) {
        console.log(`   Text: "${el.textContent}"`);
      }
    });
    
    // Also check for any visible modal-like elements
    const modalElements = await page.evaluate(() => {
      const modals = [];
      const allEls = document.querySelectorAll('*');
      
      allEls.forEach(el => {
        const style = window.getComputedStyle(el);
        const className = el.className || '';
        
        // Look for elements with modal-like properties
        if ((style.position === 'fixed' || style.position === 'absolute') &&
            (style.zIndex > 1000) &&
            el.offsetParent !== null) {
          modals.push({
            tagName: el.tagName,
            className: className,
            id: el.id || '',
            zIndex: style.zIndex,
            textContent: el.textContent?.substring(0, 100)
          });
        }
      });
      
      return modals;
    });
    
    console.log('Found modal-like elements:');
    modalElements.forEach((el, index) => {
      console.log(`${index + 1}. ${el.tagName} - Class: "${el.className}" - Z-Index: ${el.zIndex}`);
      if (el.textContent) {
        console.log(`   Text: "${el.textContent}"`);
      }
    });
  });

  test('should test different SweetAlert handling approaches @debug', async ({ page }) => {
    console.log('Testing different SweetAlert handling approaches...');
    
    // Perform login
    await loginPage.fillUsername(loginPage.config.users.superAdmin.username);
    await loginPage.fillPassword(loginPage.config.users.superAdmin.password);
    await loginPage.clickLogin();
    
    // Wait for potential SweetAlert
    await page.waitForTimeout(2000);
    
    // Approach 1: Try pressing Enter key (common SweetAlert behavior)
    console.log('Approach 1: Pressing Enter key...');
    try {
      await page.keyboard.press('Enter');
      await page.waitForTimeout(1000);
      console.log('✅ Enter key pressed');
    } catch (error) {
      console.log('❌ Enter key approach failed:', error.message);
    }
    
    // Approach 2: Try pressing Escape key
    console.log('Approach 2: Pressing Escape key...');
    try {
      await page.keyboard.press('Escape');
      await page.waitForTimeout(1000);
      console.log('✅ Escape key pressed');
    } catch (error) {
      console.log('❌ Escape key approach failed:', error.message);
    }
    
    // Approach 3: Try clicking on page center (in case of overlay)
    console.log('Approach 3: Clicking page center...');
    try {
      const viewport = page.viewportSize();
      await page.click(`${viewport.width / 2}`, `${viewport.height / 2}`);
      await page.waitForTimeout(1000);
      console.log('✅ Page center clicked');
    } catch (error) {
      console.log('❌ Page center click failed:', error.message);
    }
    
    // Check final state
    await page.waitForTimeout(2000);
    const finalURL = page.url();
    console.log('Final URL after all approaches:', finalURL);
  });
});
