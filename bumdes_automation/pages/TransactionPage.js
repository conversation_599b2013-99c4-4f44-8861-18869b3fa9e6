const BasePage = require('./BasePage');

class TransactionPage extends BasePage {
  constructor(page) {
    super(page);

    // Page selectors
    this.selectors = {
      // Transaction form
      transactionForm: '#frmTransaction',
      workunitSelect: 'select[name="workunitid"]',
      transactionTypeSelect: 'select[name="transactiontype"]',
      amountInput: 'input[name="amount"]',
      descriptionInput: 'textarea[name="transactionnote"]',
      dateInput: 'input[name="transactiondate"]',
      modalHppInput: 'input[name="modal_hpp"]',

      // Buttons
      addTransactionBtn: '.btn-add-transaction',
      submitBtn: 'button[type="submit"]',
      cancelBtn: '.btn-cancel',
      editBtn: '.btn-edit',
      deleteBtn: '.btn-delete',

      // Table
      transactionTable: '.datatables',
      tableRows: 'tbody tr',

      // Filters
      dateFromFilter: 'input[name="date_from"]',
      dateToFilter: 'input[name="date_to"]',
      typeFilter: 'select[name="transaction_type"]',
      workunitFilter: 'select[name="workunit_id"]',
      filterBtn: '.btn-filter',

      // Modal
      transactionModal: '#transactionModal',
      editModal: '#editTransactionModal',
      deleteModal: '#deleteModal',

      // Status elements
      statusBadge: '.status-badge',
      pendingStatus: '.status-pending',
      approvedStatus: '.status-approved',
      rejectedStatus: '.status-rejected',

      // Export
      exportBtn: '.btn-export',

      // Pagination
      pagination: '.pagination',
      nextPageBtn: '.pagination .next',
      prevPageBtn: '.pagination .prev'
    };
  }

  /**
   * Navigate to transaction page
   */
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.transactions);
  }

  /**
   * Verify transaction page is loaded
   */
  async verifyTransactionPageLoaded() {
    await this.verifyCurrentURL('transaction');
    await this.waitForTable();
  }

  /**
   * Click add transaction button
   */
  async clickAddTransaction() {
    await this.click(this.selectors.addTransactionBtn);
    await this.waitForElement(this.selectors.transactionModal);
  }

  /**
   * Fill transaction form
   */
  async fillTransactionForm(transactionData) {
    // Select work unit
    if (transactionData.workunitId) {
      await this.selectOption(this.selectors.workunitSelect, transactionData.workunitId);
    }

    // Select transaction type
    if (transactionData.type) {
      await this.selectOption(this.selectors.transactionTypeSelect, transactionData.type);
    }

    // Fill amount
    if (transactionData.amount) {
      await this.fill(this.selectors.amountInput, transactionData.amount.toString());
    }

    // Fill description
    if (transactionData.description) {
      await this.fill(this.selectors.descriptionInput, transactionData.description);
    }

    // Fill date
    if (transactionData.date) {
      await this.fill(this.selectors.dateInput, transactionData.date);
    }

    // Fill modal/HPP if it's income transaction
    if (transactionData.modalHpp && await this.isVisible(this.selectors.modalHppInput)) {
      await this.fill(this.selectors.modalHppInput, transactionData.modalHpp.toString());
    }
  }

  /**
   * Submit transaction form
   */
  async submitTransaction() {
    await this.click(this.selectors.submitBtn);
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Create new transaction
   */
  async createTransaction(transactionData) {
    await this.clickAddTransaction();
    await this.fillTransactionForm(transactionData);
    await this.submitTransaction();
  }

  /**
   * Edit transaction by index
   */
  async editTransaction(rowIndex, newData) {
    const editBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.editBtn}`;
    await this.click(editBtnSelector);
    await this.waitForElement(this.selectors.editModal);
    await this.fillTransactionForm(newData);
    await this.submitTransaction();
  }

  /**
   * Delete transaction by index
   */
  async deleteTransaction(rowIndex) {
    const deleteBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.deleteBtn}`;
    await this.click(deleteBtnSelector);
    await this.waitForElement(this.selectors.deleteModal);
    await this.click('.btn-confirm-delete');
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Apply date filter
   */
  async applyDateFilter(dateFrom, dateTo) {
    if (dateFrom) {
      await this.fill(this.selectors.dateFromFilter, dateFrom);
    }
    if (dateTo) {
      await this.fill(this.selectors.dateToFilter, dateTo);
    }
    await this.click(this.selectors.filterBtn);
    await this.page.waitForTimeout(2000);
  }

  /**
   * Apply transaction type filter
   */
  async applyTypeFilter(type) {
    await this.selectOption(this.selectors.typeFilter, type);
    await this.click(this.selectors.filterBtn);
    await this.page.waitForTimeout(2000);
  }

  /**
   * Apply work unit filter
   */
  async applyWorkunitFilter(workunitId) {
    await this.selectOption(this.selectors.workunitFilter, workunitId);
    await this.click(this.selectors.filterBtn);
    await this.page.waitForTimeout(2000);
  }

  /**
   * Get transaction count
   */
  async getTransactionCount() {
    return await this.getTableRowCount();
  }

  /**
   * Get transaction data from table row
   */
  async getTransactionData(rowIndex) {
    const row = `${this.selectors.tableRows}:nth-child(${rowIndex + 1})`;

    if (await this.isVisible(row)) {
      const cells = await this.page.locator(`${row} td`).allTextContents();
      return {
        code: cells[0] || '',
        date: cells[1] || '',
        workunit: cells[2] || '',
        type: cells[3] || '',
        amount: cells[4] || '',
        description: cells[5] || '',
        status: cells[6] || ''
      };
    }
    return null;
  }

  /**
   * Search transaction by code
   */
  async searchTransactionByCode(transactionCode) {
    await this.searchInTable(transactionCode);
    const count = await this.getTransactionCount();
    return count > 0;
  }

  /**
   * Verify transaction in table
   */
  async verifyTransactionInTable(transactionData) {
    const count = await this.getTransactionCount();

    for (let i = 0; i < count; i++) {
      const rowData = await this.getTransactionData(i);

      if (rowData &&
        rowData.amount.includes(transactionData.amount.toString()) &&
        rowData.type === transactionData.type) {
        return true;
      }
    }
    return false;
  }

  /**
   * Export transactions
   */
  async exportTransactions() {
    if (await this.isVisible(this.selectors.exportBtn)) {
      await this.click(this.selectors.exportBtn);
      await this.page.waitForTimeout(3000); // Wait for download
      return true;
    }
    return false;
  }

  /**
   * Test transaction form validation
   */
  async testFormValidation() {
    await this.clickAddTransaction();

    // Try to submit empty form
    await this.submitTransaction();

    // Check for validation messages
    const hasValidationErrors = await this.isVisible('.invalid-feedback') ||
      await this.isVisible('.alert-danger');

    return hasValidationErrors;
  }

  /**
   * Test transaction amount validation
   */
  async testAmountValidation() {
    await this.clickAddTransaction();

    const invalidAmounts = ['abc', '-100', '0', '999999999999999'];
    const results = [];

    for (const amount of invalidAmounts) {
      await this.fill(this.selectors.amountInput, amount);
      await this.submitTransaction();

      const hasError = await this.isVisible('.invalid-feedback') ||
        await this.isVisible('.alert-danger');

      results.push({
        amount,
        rejected: hasError
      });

      // Clear the field
      await this.fill(this.selectors.amountInput, '');
    }

    return results;
  }

  /**
   * Test pagination functionality
   */
  async testPagination() {
    const initialCount = await this.getTransactionCount();

    if (await this.isVisible(this.selectors.nextPageBtn)) {
      await this.click(this.selectors.nextPageBtn);
      await this.page.waitForTimeout(2000);

      const newCount = await this.getTransactionCount();

      // Go back to first page
      if (await this.isVisible(this.selectors.prevPageBtn)) {
        await this.click(this.selectors.prevPageBtn);
        await this.page.waitForTimeout(2000);
      }

      return {
        paginationWorks: true,
        initialCount,
        secondPageCount: newCount
      };
    }

    return {
      paginationWorks: false,
      initialCount,
      secondPageCount: 0
    };
  }

  /**
   * Verify transaction status changes
   */
  async verifyStatusChange(rowIndex, expectedStatus) {
    const statusSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.statusBadge}`;

    if (await this.isVisible(statusSelector)) {
      const statusText = await this.getText(statusSelector);
      return statusText.includes(expectedStatus);
    }
    return false;
  }

  /**
   * Test transaction CRUD operations
   */
  async testCRUDOperations(testData) {
    const results = {
      create: false,
      read: false,
      update: false,
      delete: false
    };

    try {
      // Create
      await this.createTransaction(testData);
      results.create = await this.verifyTransactionInTable(testData);

      if (results.create) {
        // Read
        results.read = await this.searchTransactionByCode(testData.code || '');

        // Update
        const updatedData = { ...testData, amount: testData.amount + 1000 };
        await this.editTransaction(0, updatedData);
        results.update = await this.verifyTransactionInTable(updatedData);

        // Delete
        await this.deleteTransaction(0);
        results.delete = !(await this.verifyTransactionInTable(updatedData));
      }
    } catch (error) {
      console.error('CRUD test error:', error);
    }

    return results;
  }

  /**
   * Test modal/HPP functionality for income transactions
   */
  async testModalHppFunctionality() {
    await this.clickAddTransaction();

    // Select income transaction type
    await this.selectOption(this.selectors.transactionTypeSelect, 'Pendapatan');
    await this.page.waitForTimeout(1000);

    // Check if modal/HPP field appears
    const modalHppVisible = await this.isVisible(this.selectors.modalHppInput);

    if (modalHppVisible) {
      // Fill transaction data with HPP
      const testData = {
        workunitId: '1',
        type: 'Pendapatan',
        amount: 1000000,
        description: 'Test income with HPP',
        modalHpp: 600000
      };

      await this.fillTransactionForm(testData);
      await this.submitTransaction();

      // Verify that expense transaction was auto-created
      await this.page.waitForTimeout(2000);
      const hasExpenseTransaction = await this.searchTransactionByCode('');

      return {
        modalHppFieldVisible: true,
        autoExpenseCreated: hasExpenseTransaction
      };
    }

    return {
      modalHppFieldVisible: false,
      autoExpenseCreated: false
    };
  }

  /**
   * Test beginning balance requirement
   */
  async testBeginningBalanceRequirement() {
    const testData = this.helpers.generateTestData().transaction;

    try {
      await this.createTransaction(testData);

      // Check if there's an error about beginning balance
      const hasBalanceError = await this.isVisible('.alert-danger');

      if (hasBalanceError) {
        const errorText = await this.getText('.alert-danger');
        return {
          requiresBeginningBalance: errorText.includes('saldo awal') || errorText.includes('beginning balance'),
          errorMessage: errorText
        };
      }

      return {
        requiresBeginningBalance: false,
        errorMessage: null
      };
    } catch (error) {
      return {
        requiresBeginningBalance: true,
        errorMessage: error.message
      };
    }
  }
}

module.exports = TransactionPage;
