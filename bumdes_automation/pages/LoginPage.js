const BasePage = require('./BasePage');

class LoginPage extends BasePage {
  constructor(page) {
    super(page);

    // Page selectors
    this.selectors = {
      loginForm: '#frmLogin',
      usernameInput: 'input[name="username"]',
      passwordInput: 'input[name="password"]',
      loginButton: 'button[type="submit"]',
      errorMessage: '.alert-danger',
      forgotPasswordLink: 'a[href*="forgot"]',
      registerLink: 'a[href*="register"]',

      // SweetAlert selectors (v1)
      sweetAlert: '.sweet-alert',
      sweetAlertTitle: '.sweet-alert h2',
      sweetAlertContent: '.sweet-alert p',
      sweetAlertOkButton: '.sweet-alert .confirm',
      sweetAlertCancelButton: '.sweet-alert .cancel',
      sweetAlertError: '.swal-icon--error',
      sweetAlertSuccess: '.swal-icon--success'
    };
  }

  /**
   * Navigate to login page
   */
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.login);
  }

  /**
   * Fill username field
   */
  async fillUsername(username) {
    await this.fill(this.selectors.usernameInput, username);
  }

  /**
   * Fill password field
   */
  async fillPassword(password) {
    await this.fill(this.selectors.passwordInput, password);
  }

  /**
   * Click login button
   */
  async clickLogin() {
    await this.click(this.selectors.loginButton);
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Wait for and handle SweetAlert after login
   */
  async handleLoginSweetAlert() {
    // Use the base class method for handling SweetAlert
    const result = await this.handleSweetAlert('confirm');
    return result.handled;
  }

  /**
   * Check for and handle login error SweetAlert
   */
  async checkForLoginError() {
    try {
      // Wait for error icon to appear
      await this.waitForElement(this.selectors.sweetAlertError, 3000);

      // Get error message (use flexible selectors)
      let errorTitle = '';
      let errorContent = '';

      // Try different selectors for error alert content
      const titleSelectors = [
        '.sweet-alert .swal-title',
        '.sweet-alert .sweet-alert-title',
        '.sweet-alert .title',
        '.sweet-alert h2',
        '.sweet-alert div:first-child'
      ];

      const contentSelectors = [
        '.sweet-alert .swal-text',
        '.sweet-alert .sweet-alert-text',
        '.sweet-alert p',
        '.sweet-alert .content'
      ];

      // Try to get title
      for (const selector of titleSelectors) {
        try {
          if (await this.isVisible(selector)) {
            errorTitle = await this.getText(selector);
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }

      // Try to get content
      for (const selector of contentSelectors) {
        try {
          if (await this.isVisible(selector)) {
            errorContent = await this.getText(selector);
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }

      console.log('Login error detected:', { title: errorTitle, content: errorContent });

      // Click OK to dismiss error
      if (await this.isVisible(this.selectors.sweetAlertOkButton)) {
        await this.click(this.selectors.sweetAlertOkButton);
      }

      // Wait for alert to disappear
      await this.page.waitForSelector(this.selectors.sweetAlert, { state: 'hidden', timeout: 5000 });

      return {
        hasError: true,
        title: errorTitle,
        content: errorContent
      };
    } catch (error) {
      // No error alert found
      return {
        hasError: false,
        error: error.message
      };
    }
  }

  /**
   * Check for success SweetAlert and handle it (MUST click OK for redirect)
   */
  async checkForLoginSuccess() {
    try {
      // Wait for success icon to appear
      await this.waitForElement(this.selectors.sweetAlertSuccess, 3000);

      // Get success message (avoid using h2 selector for success alerts)
      let successTitle = '';
      let successContent = '';

      // Try different selectors for success alert content
      const titleSelectors = [
        '.sweet-alert .swal-title',
        '.sweet-alert .sweet-alert-title',
        '.sweet-alert .title',
        '.sweet-alert div:first-child'
      ];

      const contentSelectors = [
        '.sweet-alert .swal-text',
        '.sweet-alert .sweet-alert-text',
        '.sweet-alert p',
        '.sweet-alert .content'
      ];

      // Try to get title
      for (const selector of titleSelectors) {
        try {
          if (await this.isVisible(selector)) {
            successTitle = await this.getText(selector);
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }

      // Try to get content
      for (const selector of contentSelectors) {
        try {
          if (await this.isVisible(selector)) {
            successContent = await this.getText(selector);
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }

      console.log('Login success detected:', { title: successTitle, content: successContent });
      console.log('Clicking OK button to proceed to dashboard...');

      // IMPORTANT: Must click OK to proceed to dashboard
      let buttonClicked = false;

      // Try multiple possible OK button selectors
      const okButtonSelectors = [
        this.selectors.sweetAlertOkButton,
        '.sweet-alert .confirm',
        '.sweet-alert button',
        '.swal-button--confirm',
        'button:contains("OK")',
        'button:contains("Oke")',
        'button:contains("Ya")'
      ];

      for (const btnSelector of okButtonSelectors) {
        try {
          if (await this.isVisible(btnSelector)) {
            console.log(`Clicking OK button with selector: ${btnSelector}`);
            await this.click(btnSelector);
            buttonClicked = true;
            break;
          }
        } catch (error) {
          // Continue trying other selectors
        }
      }

      if (!buttonClicked) {
        // Fallback: press Enter key
        console.log('Fallback: pressing Enter key to dismiss success alert');
        await this.page.keyboard.press('Enter');
        buttonClicked = true;
      }

      // Wait for alert to disappear
      await this.page.waitForSelector(this.selectors.sweetAlert, { state: 'hidden', timeout: 5000 });
      console.log('✅ Success alert dismissed, should redirect to dashboard now');

      return {
        hasSuccess: true,
        title: successTitle,
        content: successContent,
        buttonClicked: buttonClicked
      };
    } catch (error) {
      // No success alert found
      return {
        hasSuccess: false,
        error: error.message
      };
    }
  }

  /**
   * Perform login with credentials
   */
  async login(username, password) {
    await this.fillUsername(username);
    await this.fillPassword(password);
    await this.clickLogin();

    // Wait a moment for SweetAlert to appear
    await this.page.waitForTimeout(2000);

    // Check for error first
    const errorResult = await this.checkForLoginError();
    if (errorResult.hasError) {
      console.log('Login failed with error:', errorResult);
      return { success: false, error: errorResult };
    }

    // Check for success and handle it (click OK to proceed to dashboard)
    const successResult = await this.checkForLoginSuccess();
    if (successResult.hasSuccess) {
      console.log('Login succeeded, SweetAlert dismissed:', successResult);
      // Success alert was already dismissed in checkForLoginSuccess method
      // Now wait for redirect to dashboard
      await this.page.waitForTimeout(2000);
      return { success: true, message: successResult };
    }

    // Fallback: try generic SweetAlert handling
    const genericResult = await this.handleLoginSweetAlert();
    if (genericResult) {
      // Wait for potential redirect after generic handling
      await this.page.waitForTimeout(2000);
    }
    return { success: genericResult, fallback: true };
  }

  /**
   * Login as Super Admin (handles SweetAlert success automatically)
   */
  async loginAsSuperAdmin() {
    const user = this.config.users.superAdmin;
    const result = await this.login(user.username, user.password);
    if (!result.success) {
      throw new Error(`Super Admin login failed: ${JSON.stringify(result)}`);
    }
    return result;
  }

  /**
   * Login as BUMDes (handles SweetAlert success automatically)
   */
  async loginAsBumdes() {
    const user = this.config.users.bumdes;
    const result = await this.login(user.username, user.password);
    if (!result.success) {
      throw new Error(`BUMDes login failed: ${JSON.stringify(result)}`);
    }
    return result;
  }

  /**
   * Login as BUMDes User (handles SweetAlert success automatically)
   */
  async loginAsBumdesUser() {
    const user = this.config.users.bumdesUser;
    const result = await this.login(user.username, user.password);
    if (!result.success) {
      throw new Error(`BUMDes User login failed: ${JSON.stringify(result)}`);
    }
    return result;
  }

  /**
   * Verify login form is visible
   */
  async verifyLoginFormVisible() {
    await this.verifyElementVisible(this.selectors.loginForm);
    await this.verifyElementVisible(this.selectors.usernameInput);
    await this.verifyElementVisible(this.selectors.passwordInput);
    await this.verifyElementVisible(this.selectors.loginButton);
  }

  /**
   * Verify login error message
   */
  async verifyLoginError() {
    await this.waitForElement(this.selectors.errorMessage);
    const errorText = await this.getText(this.selectors.errorMessage);
    if (!errorText) {
      throw new Error('Expected error message to be present, but it was empty');
    }
    return errorText;
  }

  /**
   * Verify successful login (redirect to dashboard with sidebar after SweetAlert)
   */
  async verifySuccessfulLogin() {
    console.log('Verifying successful login and dashboard redirect...');

    // Wait longer for redirect after SweetAlert OK button is clicked
    await this.page.waitForTimeout(5000);
    await this.helpers.waitForPageLoad(this.page);

    const currentURL = await this.getCurrentURL();
    console.log('Current URL after login:', currentURL);

    if (!currentURL.includes('dashboard')) {
      // If not on dashboard yet, wait a bit more
      console.log('Not on dashboard yet, waiting additional time...');
      await this.page.waitForTimeout(3000);
      const updatedURL = await this.getCurrentURL();
      console.log('Updated URL:', updatedURL);

      if (!updatedURL.includes('dashboard')) {
        throw new Error(`Expected URL to contain 'dashboard', but got "${updatedURL}". SweetAlert OK button may not have been clicked properly.`);
      }
    }

    // Also verify sidebar is visible as indicator of successful login
    console.log('Checking for sidebar visibility...');
    const sidebarVisible = await this.isVisible('.sidebar');
    if (!sidebarVisible) {
      console.log('Warning: Dashboard URL reached but sidebar not visible yet, waiting...');
      await this.page.waitForTimeout(3000);
      const sidebarVisibleAfterWait = await this.isVisible('.sidebar');
      if (!sidebarVisibleAfterWait) {
        throw new Error('Dashboard reached but sidebar not visible - login may not be complete');
      }
    }

    console.log('✅ Login verification successful - on dashboard with sidebar visible');
    return true;
  }

  /**
   * Click register link
   */
  async clickRegisterLink() {
    if (await this.isVisible(this.selectors.registerLink)) {
      await this.click(this.selectors.registerLink);
      await this.helpers.waitForPageLoad(this.page);
    }
  }

  /**
   * Click forgot password link
   */
  async clickForgotPasswordLink() {
    if (await this.isVisible(this.selectors.forgotPasswordLink)) {
      await this.click(this.selectors.forgotPasswordLink);
      await this.helpers.waitForPageLoad(this.page);
    }
  }

  /**
   * Test login with invalid credentials
   */
  async testInvalidLogin(username = 'invalid', password = 'invalid') {
    const loginResult = await this.login(username, password);

    if (loginResult.success === false && loginResult.error) {
      // Error was handled by login method
      return loginResult.error.content || loginResult.error.title || 'Login failed';
    }

    // Fallback: check for traditional error message
    return await this.verifyLoginError();
  }

  /**
   * Test login with empty credentials
   */
  async testEmptyLogin() {
    await this.login('', '');
    // Check for validation messages or error
    const hasError = await this.isVisible(this.selectors.errorMessage);
    return hasError;
  }

  /**
   * Test login with SQL injection attempts
   */
  async testSQLInjectionLogin() {
    const sqlInjectionAttempts = [
      "' OR '1'='1",
      "admin'--",
      "' OR 1=1--",
      "admin' OR '1'='1'#"
    ];

    const results = [];
    for (const attempt of sqlInjectionAttempts) {
      await this.login(attempt, attempt);
      const hasError = await this.isVisible(this.selectors.errorMessage);
      results.push({
        attempt,
        blocked: hasError
      });

      // Clear form for next attempt
      await this.fillUsername('');
      await this.fillPassword('');
    }

    return results;
  }

  /**
   * Verify page elements and accessibility
   */
  async verifyPageAccessibility() {
    // Check if all form elements have proper labels
    const usernameLabel = await this.page.getAttribute(this.selectors.usernameInput, 'placeholder');
    const passwordLabel = await this.page.getAttribute(this.selectors.passwordInput, 'placeholder');

    if (!usernameLabel) {
      throw new Error('Username input should have a placeholder');
    }
    if (!passwordLabel) {
      throw new Error('Password input should have a placeholder');
    }

    // Check if form is keyboard accessible
    await this.page.keyboard.press('Tab');
    const focusedElement = await this.page.evaluate(() => document.activeElement.tagName);
    if (focusedElement !== 'INPUT') {
      throw new Error(`Expected focused element to be INPUT, but got ${focusedElement}`);
    }

    return true;
  }

  /**
   * Test password visibility toggle (if exists)
   */
  async testPasswordVisibilityToggle() {
    const toggleSelector = '.toggle-password';
    if (await this.isVisible(toggleSelector)) {
      // Fill password first
      await this.fillPassword('testpassword');

      // Check initial type
      const initialType = await this.page.getAttribute(this.selectors.passwordInput, 'type');
      if (initialType !== 'password') {
        throw new Error(`Expected initial password type to be 'password', but got '${initialType}'`);
      }

      // Click toggle
      await this.click(toggleSelector);

      // Check if type changed to text
      const toggledType = await this.page.getAttribute(this.selectors.passwordInput, 'type');
      if (toggledType !== 'text') {
        throw new Error(`Expected toggled password type to be 'text', but got '${toggledType}'`);
      }

      // Toggle back
      await this.click(toggleSelector);
      const finalType = await this.page.getAttribute(this.selectors.passwordInput, 'type');
      if (finalType !== 'password') {
        throw new Error(`Expected final password type to be 'password', but got '${finalType}'`);
      }

      return true;
    }
    return false;
  }
}

module.exports = LoginPage;
