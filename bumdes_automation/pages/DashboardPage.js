const BasePage = require('./BasePage');

class DashboardPage extends BasePage {
  constructor(page) {
    super(page);

    // Page selectors
    this.selectors = {
      // Dashboard cards
      totalModalCard: '.card:has-text("Total Modal")',
      totalOmsetCard: '.card:has-text("Total Omset")',
      totalLabaCard: '.card:has-text("Total Laba")',
      totalBumdesCard: '.card:has-text("Total BUMDes")',

      // Charts
      omsetChart: '#omsetChart',
      labaChart: '#labaChart',

      // Tables
      bumdesOverviewTable: '.bumdes-overview-table',
      recentTransactionsTable: '.recent-transactions-table',

      // Filters
      yearFilter: 'select[name="year"]',
      bumdesFilter: 'select[name="bumdes_id"]',

      // Navigation
      sidebar: '.sidebar',
      navbar: '.navbar',
      userInfo: '.user-info',

      // Quick actions
      addTransactionBtn: '.btn-add-transaction',
      viewReportsBtn: '.btn-view-reports',

      // Monitoring section (Super Admin)
      monitoringSection: '.monitoring-section',
      monitoringTable: '#dataTable',

      // BUMDes overview
      bumdesOverviewSection: '.bumdes-overview-section'
    };
  }

  /**
   * Navigate to dashboard
   */
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.dashboard);
  }

  /**
   * Verify dashboard page is loaded
   */
  async verifyDashboardLoaded() {
    await this.verifyCurrentURL('dashboard');
    await this.verifyElementVisible(this.selectors.sidebar);
    // Note: Using .sidebar as main indicator of successful dashboard load
  }

  /**
   * Get total modal value
   */
  async getTotalModal() {
    if (await this.isVisible(this.selectors.totalModalCard)) {
      return await this.getText(this.selectors.totalModalCard + ' .card-value');
    }
    return null;
  }

  /**
   * Get total omset value
   */
  async getTotalOmset() {
    if (await this.isVisible(this.selectors.totalOmsetCard)) {
      return await this.getText(this.selectors.totalOmsetCard + ' .card-value');
    }
    return null;
  }

  /**
   * Get total laba value
   */
  async getTotalLaba() {
    if (await this.isVisible(this.selectors.totalLabaCard)) {
      return await this.getText(this.selectors.totalLabaCard + ' .card-value');
    }
    return null;
  }

  /**
   * Get total BUMDes count (Super Admin only)
   */
  async getTotalBumdes() {
    if (await this.isVisible(this.selectors.totalBumdesCard)) {
      return await this.getText(this.selectors.totalBumdesCard + ' .card-value');
    }
    return null;
  }

  /**
   * Verify dashboard cards are visible
   */
  async verifyDashboardCards() {
    const cards = [
      this.selectors.totalModalCard,
      this.selectors.totalOmsetCard,
      this.selectors.totalLabaCard
    ];

    for (const card of cards) {
      if (await this.isVisible(card)) {
        await this.verifyElementVisible(card);
      }
    }
  }

  /**
   * Verify charts are loaded
   */
  async verifyChartsLoaded() {
    if (await this.isVisible(this.selectors.omsetChart)) {
      await this.verifyElementVisible(this.selectors.omsetChart);
    }

    if (await this.isVisible(this.selectors.labaChart)) {
      await this.verifyElementVisible(this.selectors.labaChart);
    }
  }

  /**
   * Change year filter
   */
  async changeYearFilter(year) {
    if (await this.isVisible(this.selectors.yearFilter)) {
      await this.selectOption(this.selectors.yearFilter, year);
      await this.page.waitForTimeout(2000); // Wait for data to reload
    }
  }

  /**
   * Change BUMDes filter (Super Admin only)
   */
  async changeBumdesFilter(bumdesId) {
    if (await this.isVisible(this.selectors.bumdesFilter)) {
      await this.selectOption(this.selectors.bumdesFilter, bumdesId);
      await this.page.waitForTimeout(2000); // Wait for data to reload
    }
  }

  /**
   * Verify user information is displayed
   */
  async verifyUserInfo() {
    if (await this.isVisible(this.selectors.userInfo)) {
      const userText = await this.getText(this.selectors.userInfo);
      expect(userText).toBeTruthy();
      return userText;
    }
    return null;
  }

  /**
   * Click add transaction button
   */
  async clickAddTransaction() {
    if (await this.isVisible(this.selectors.addTransactionBtn)) {
      await this.click(this.selectors.addTransactionBtn);
      await this.helpers.waitForPageLoad(this.page);
    }
  }

  /**
   * Click view reports button
   */
  async clickViewReports() {
    if (await this.isVisible(this.selectors.viewReportsBtn)) {
      await this.click(this.selectors.viewReportsBtn);
      await this.helpers.waitForPageLoad(this.page);
    }
  }

  /**
   * Verify monitoring section (Super Admin only)
   */
  async verifyMonitoringSection() {
    if (await this.isVisible(this.selectors.monitoringSection)) {
      await this.verifyElementVisible(this.selectors.monitoringSection);
      await this.verifyElementVisible(this.selectors.monitoringTable);
      return true;
    }
    return false;
  }

  /**
   * Get monitoring table data
   */
  async getMonitoringTableData() {
    if (await this.isVisible(this.selectors.monitoringTable)) {
      const rowCount = await this.helpers.getTableRowCount(this.page, this.selectors.monitoringTable + ' tbody tr');
      return rowCount;
    }
    return 0;
  }

  /**
   * Verify BUMDes overview section
   */
  async verifyBumdesOverviewSection() {
    if (await this.isVisible(this.selectors.bumdesOverviewSection)) {
      await this.verifyElementVisible(this.selectors.bumdesOverviewSection);
      return true;
    }
    return false;
  }

  /**
   * Navigate to specific menu item
   */
  async navigateToMenuItem(menuItem) {
    const menuItems = {
      'Dashboard': '/dashboard',
      'Master BUMDes': '/master/bumdes',
      'Unit Usaha': '/master/workunits',
      'Pengguna BUMDes': '/bumdes_users',
      'Transaksi': '/transaction',
      'Saldo Awal': '/beginning_balance',
      'Laporan': '/reports',
      'History Transaksi': '/transaction_history'
    };

    if (menuItems[menuItem]) {
      await this.navigateToMenu(menuItem);
      await this.verifyCurrentURL(menuItems[menuItem]);
    }
  }

  /**
   * Test dashboard responsiveness
   */
  async testResponsiveness() {
    const viewports = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1366, height: 768 },  // Laptop
      { width: 768, height: 1024 },  // Tablet
      { width: 375, height: 667 }    // Mobile
    ];

    const results = [];

    for (const viewport of viewports) {
      await this.page.setViewportSize(viewport);
      await this.page.waitForTimeout(1000);

      const isSidebarVisible = await this.isVisible(this.selectors.sidebar);

      results.push({
        viewport,
        sidebarVisible: isSidebarVisible
      });
    }

    return results;
  }

  /**
   * Verify dashboard data consistency
   */
  async verifyDataConsistency() {
    const modal = await this.getTotalModal();
    const omset = await this.getTotalOmset();
    const laba = await this.getTotalLaba();

    // Basic validation that values are numbers or formatted currency
    const isValidCurrency = (value) => {
      if (!value) return false;
      return /^Rp\s[\d.,]+$/.test(value) || /^\d+$/.test(value);
    };

    return {
      modal: { value: modal, valid: isValidCurrency(modal) },
      omset: { value: omset, valid: isValidCurrency(omset) },
      laba: { value: laba, valid: isValidCurrency(laba) }
    };
  }

  /**
   * Test dashboard performance
   */
  async testPerformance() {
    const startTime = Date.now();
    await this.goto();
    await this.verifyDashboardLoaded();
    const loadTime = Date.now() - startTime;

    return {
      loadTime,
      acceptable: loadTime < 5000 // 5 seconds threshold
    };
  }
}

module.exports = DashboardPage;
