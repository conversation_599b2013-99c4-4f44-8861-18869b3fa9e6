const config = require('../utils/config');
const TestHelpers = require('../utils/helpers');

class BasePage {
  constructor(page) {
    this.page = page;
    this.config = config;
    this.helpers = TestHelpers;
  }

  /**
   * Navigate to a specific URL
   */
  async goto(url) {
    await this.page.goto(url);
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Navigate to base URL
   */
  async gotoHome() {
    await this.goto(this.config.baseURL);
  }

  /**
   * Get page title
   */
  async getTitle() {
    return await this.page.title();
  }

  /**
   * Get current URL
   */
  async getCurrentURL() {
    return this.page.url();
  }

  /**
   * Wait for element to be visible
   */
  async waitForElement(selector, timeout = 30000) {
    await this.helpers.waitForElement(this.page, selector, timeout);
  }

  /**
   * Click element
   */
  async click(selector) {
    await this.page.click(selector);
  }

  /**
   * Fill input field
   */
  async fill(selector, value) {
    await this.helpers.clearAndFill(this.page, selector, value);
  }

  /**
   * Select option from dropdown
   */
  async selectOption(selector, value) {
    await this.helpers.selectOption(this.page, selector, value);
  }

  /**
   * Get text content
   */
  async getText(selector) {
    return await this.helpers.getElementText(this.page, selector);
  }

  /**
   * Check if element is visible
   */
  async isVisible(selector) {
    try {
      await this.waitForElement(selector, 5000);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if element exists
   */
  async exists(selector) {
    return await this.helpers.elementExists(this.page, selector);
  }

  /**
   * Take screenshot
   */
  async takeScreenshot(name) {
    return await this.helpers.takeScreenshot(this.page, name);
  }

  /**
   * Wait for success message
   */
  async waitForSuccessMessage() {
    await this.waitForElement(this.config.selectors.successAlert);
  }

  /**
   * Wait for error message
   */
  async waitForErrorMessage() {
    await this.waitForElement(this.config.selectors.errorAlert);
  }

  /**
   * Get success message text
   */
  async getSuccessMessage() {
    return await this.getText(this.config.selectors.successAlert);
  }

  /**
   * Get error message text
   */
  async getErrorMessage() {
    return await this.getText(this.config.selectors.errorAlert);
  }

  /**
   * Close modal
   */
  async closeModal() {
    if (await this.isVisible(this.config.selectors.closeModal)) {
      await this.click(this.config.selectors.closeModal);
    }
  }

  /**
   * Handle SweetAlert v1 (Success, Error, Warning, etc.)
   */
  async handleSweetAlert(action = 'confirm') {
    // Multiple possible selectors for SweetAlert v1
    const possibleContainers = [
      '.sweet-alert',
      '.swal-overlay',
      '.swal-modal',
      '[class*="sweet-alert"]',
      '[class*="swal"]'
    ];

    // Check for specific error or success icons first
    const errorIcon = '.swal-icon--error';
    const successIcon = '.swal-icon--success';

    const possibleConfirmButtons = [
      '.sweet-alert .confirm',
      '.sweet-alert button',
      '.swal-button--confirm',
      '.sa-confirm-button-container button',
      '[class*="confirm"]',
      'button:contains("OK")',
      'button:contains("Ya")',
      'button:contains("Oke")'
    ];

    try {
      let foundContainer = null;
      let alertVisible = false;
      let alertType = 'unknown';

      // First check for specific error or success icons
      try {
        const errorVisible = await this.page.isVisible(errorIcon);
        const successVisible = await this.page.isVisible(successIcon);

        if (errorVisible) {
          alertType = 'error';
          alertVisible = true;
          console.log('Error SweetAlert detected');
        } else if (successVisible) {
          alertType = 'success';
          alertVisible = true;
          console.log('Success SweetAlert detected');
        }
      } catch (error) {
        // Continue with container detection
      }

      // Try to find SweetAlert container
      if (!alertVisible) {
        for (const containerSelector of possibleContainers) {
          try {
            await this.page.waitForSelector(containerSelector, { timeout: 2000 });
            const isVisible = await this.page.isVisible(containerSelector);
            if (isVisible) {
              foundContainer = containerSelector;
              alertVisible = true;
              break;
            }
          } catch (error) {
            // Continue trying other selectors
          }
        }
      }

      if (!alertVisible) {
        // Try alternative approach - check for any modal-like elements
        const hasModal = await this.page.evaluate(() => {
          const elements = document.querySelectorAll('*');
          for (let el of elements) {
            const style = window.getComputedStyle(el);
            const className = el.className || '';

            if ((style.position === 'fixed' || style.position === 'absolute') &&
              style.zIndex > 1000 &&
              el.offsetParent !== null &&
              (className.includes('sweet') || className.includes('swal') || className.includes('alert'))) {
              return true;
            }
          }
          return false;
        });

        if (!hasModal) {
          return { handled: false, error: 'No SweetAlert found' };
        }
      }

      console.log('SweetAlert detected with container:', foundContainer);

      // Get alert content if possible
      let title = '';
      let content = '';

      try {
        // More flexible selectors, avoiding h2 for success alerts
        const titleSelectors = [
          `${foundContainer} .swal-title`,
          `${foundContainer} .sweet-alert-title`,
          `${foundContainer} .title`,
          `${foundContainer} h2` // Keep h2 as last option for error alerts
        ];

        for (const titleSelector of titleSelectors) {
          if (await this.isVisible(titleSelector)) {
            title = await this.getText(titleSelector);
            break;
          }
        }

        const contentSelectors = [
          `${foundContainer} .swal-text`,
          `${foundContainer} .sweet-alert-text`,
          `${foundContainer} .content`,
          `${foundContainer} p`
        ];

        for (const contentSelector of contentSelectors) {
          if (await this.isVisible(contentSelector)) {
            content = await this.getText(contentSelector);
            break;
          }
        }
      } catch (error) {
        console.log('Could not get alert content:', error.message);
      }

      console.log('SweetAlert content:', { title, content, action });

      // Handle the alert based on action
      let buttonClicked = false;

      if (action.toLowerCase() === 'confirm' || action.toLowerCase() === 'ok') {
        // Try to find and click confirm button
        for (const btnSelector of possibleConfirmButtons) {
          try {
            const fullSelector = foundContainer ? `${foundContainer} ${btnSelector.replace(foundContainer + ' ', '')}` : btnSelector;
            if (await this.isVisible(fullSelector)) {
              await this.click(fullSelector);
              buttonClicked = true;
              console.log('Clicked confirm button:', fullSelector);
              break;
            }
          } catch (error) {
            // Continue trying other button selectors
          }
        }

        // Fallback approaches
        if (!buttonClicked) {
          try {
            // Try pressing Enter key
            await this.page.keyboard.press('Enter');
            buttonClicked = true;
            console.log('Used Enter key as fallback');
          } catch (error) {
            // Try clicking on the alert container
            if (foundContainer) {
              await this.click(foundContainer);
              buttonClicked = true;
              console.log('Clicked on alert container as fallback');
            }
          }
        }
      }

      // Wait for alert to disappear
      if (foundContainer) {
        try {
          await this.page.waitForSelector(foundContainer, { state: 'hidden', timeout: 5000 });
        } catch (error) {
          console.log('Alert may not have disappeared, continuing...');
        }
      }

      // Wait for any subsequent page loads
      await this.helpers.waitForPageLoad(this.page);

      return { title, content, handled: buttonClicked, container: foundContainer };
    } catch (error) {
      console.log('SweetAlert handling failed:', error.message);
      return { handled: false, error: error.message };
    }
  }

  /**
   * Submit form
   */
  async submitForm(formSelector = this.config.selectors.submitButton) {
    await this.click(formSelector);
  }

  /**
   * Cancel form
   */
  async cancelForm() {
    await this.click(this.config.selectors.cancelButton);
  }

  /**
   * Wait for table to load
   */
  async waitForTable() {
    await this.waitForElement(this.config.selectors.dataTable);
  }

  /**
   * Get table row count
   */
  async getTableRowCount() {
    return await this.helpers.getTableRowCount(this.page);
  }

  /**
   * Search in table (if search functionality exists)
   */
  async searchInTable(searchTerm) {
    const searchInput = 'input[type="search"]';
    if (await this.exists(searchInput)) {
      await this.fill(searchInput, searchTerm);
      await this.page.keyboard.press('Enter');
      await this.page.waitForTimeout(1000); // Wait for search results
    }
  }

  /**
   * Navigate using sidebar menu
   */
  async navigateToMenu(menuText) {
    const menuSelector = `${this.config.selectors.sidebar} a:has-text("${menuText}")`;
    await this.click(menuSelector);
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Logout from application
   */
  async logout() {
    // Click user dropdown
    if (await this.isVisible(this.config.selectors.userDropdown)) {
      await this.click(this.config.selectors.userDropdown);
    }

    // Click logout button
    await this.click(this.config.selectors.logoutButton);
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Verify page title contains text
   */
  async verifyPageTitle(expectedTitle) {
    const title = await this.getTitle();
    if (!title.includes(expectedTitle)) {
      throw new Error(`Expected page title to contain "${expectedTitle}", but got "${title}"`);
    }
    return true;
  }

  /**
   * Verify current URL contains path
   */
  async verifyCurrentURL(expectedPath) {
    const currentURL = await this.getCurrentURL();
    if (!currentURL.includes(expectedPath)) {
      throw new Error(`Expected URL to contain "${expectedPath}", but got "${currentURL}"`);
    }
    return true;
  }

  /**
   * Verify element text
   */
  async verifyElementText(selector, expectedText) {
    return await this.helpers.verifyTextContent(this.page, selector, expectedText);
  }

  /**
   * Verify element is visible
   */
  async verifyElementVisible(selector) {
    return await this.helpers.verifyElementVisible(this.page, selector);
  }

  /**
   * Verify element is hidden
   */
  async verifyElementHidden(selector) {
    return await this.helpers.verifyElementHidden(this.page, selector);
  }
}

module.exports = BasePage;
