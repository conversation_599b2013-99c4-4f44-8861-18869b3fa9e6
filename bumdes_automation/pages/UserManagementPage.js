const BasePage = require('./BasePage');

class UserManagementPage extends BasePage {
  constructor(page) {
    super(page);
    
    // Page selectors
    this.selectors = {
      // User form
      userForm: '#frm',
      nameInput: 'input[name="name"]',
      phoneInput: 'input[name="phone"]',
      addressInput: 'textarea[name="address"]',
      usernameInput: 'input[name="username"]',
      passwordInput: 'input[name="password"]',
      workunitSelect: 'select[name="workunitid"]',
      
      // Buttons
      addUserBtn: '.btn-add-user',
      submitBtn: 'button[type="submit"]',
      cancelBtn: '.btn-cancel',
      editBtn: '.btn-edit',
      deleteBtn: '.btn-delete',
      
      // Table
      userTable: '.datatables',
      tableRows: 'tbody tr',
      
      // Filters
      statusFilter: 'select[name="status"]',
      workunitFilter: 'select[name="workunit"]',
      searchInput: 'input[type="search"]',
      
      // Modal
      userModal: '#userModal',
      editModal: '#editUserModal',
      deleteModal: '#deleteModal',
      
      // Status elements
      statusBadge: '.status-badge',
      activeStatus: '.status-active',
      inactiveStatus: '.status-inactive'
    };
  }
  
  /**
   * Navigate to user management page
   */
  async goto() {
    await super.goto(this.config.baseURL + this.config.urls.bumdesUsers);
  }
  
  /**
   * Verify user management page is loaded
   */
  async verifyUserManagementPageLoaded() {
    await this.verifyCurrentURL('bumdes_users');
    await this.waitForTable();
  }
  
  /**
   * Click add user button
   */
  async clickAddUser() {
    await this.click(this.selectors.addUserBtn);
    await this.waitForElement(this.selectors.userModal);
  }
  
  /**
   * Fill user form
   */
  async fillUserForm(userData) {
    // Fill name
    if (userData.name) {
      await this.fill(this.selectors.nameInput, userData.name);
    }
    
    // Fill phone
    if (userData.phone) {
      await this.fill(this.selectors.phoneInput, userData.phone);
    }
    
    // Fill address
    if (userData.address) {
      await this.fill(this.selectors.addressInput, userData.address);
    }
    
    // Fill username
    if (userData.username) {
      await this.fill(this.selectors.usernameInput, userData.username);
    }
    
    // Fill password
    if (userData.password) {
      await this.fill(this.selectors.passwordInput, userData.password);
    }
    
    // Select work unit
    if (userData.workunitId) {
      await this.selectOption(this.selectors.workunitSelect, userData.workunitId);
    }
  }
  
  /**
   * Submit user form
   */
  async submitUser() {
    await this.click(this.selectors.submitBtn);
    await this.helpers.waitForPageLoad(this.page);
  }
  
  /**
   * Create new user
   */
  async createUser(userData) {
    await this.clickAddUser();
    await this.fillUserForm(userData);
    await this.submitUser();
  }
  
  /**
   * Edit user by index
   */
  async editUser(rowIndex, newData) {
    const editBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.editBtn}`;
    await this.click(editBtnSelector);
    await this.waitForElement(this.selectors.editModal);
    await this.fillUserForm(newData);
    await this.submitUser();
  }
  
  /**
   * Delete user by index
   */
  async deleteUser(rowIndex) {
    const deleteBtnSelector = `${this.selectors.tableRows}:nth-child(${rowIndex + 1}) ${this.selectors.deleteBtn}`;
    await this.click(deleteBtnSelector);
    await this.waitForElement(this.selectors.deleteModal);
    await this.click('.btn-confirm-delete');
    await this.helpers.waitForPageLoad(this.page);
  }
  
  /**
   * Get user count
   */
  async getUserCount() {
    return await this.getTableRowCount();
  }
  
  /**
   * Get user data from table row
   */
  async getUserData(rowIndex) {
    const row = `${this.selectors.tableRows}:nth-child(${rowIndex + 1})`;
    
    if (await this.isVisible(row)) {
      const cells = await this.page.locator(`${row} td`).allTextContents();
      return {
        name: cells[1] || '',
        username: cells[2] || '',
        phone: cells[3] || '',
        workunit: cells[4] || '',
        status: cells[5] || '',
        createdDate: cells[6] || ''
      };
    }
    return null;
  }
  
  /**
   * Search user by name
   */
  async searchUserByName(name) {
    await this.searchInTable(name);
    const count = await this.getUserCount();
    return count > 0;
  }
  
  /**
   * Verify user in table
   */
  async verifyUserInTable(userData) {
    const count = await this.getUserCount();
    
    for (let i = 0; i < count; i++) {
      const rowData = await this.getUserData(i);
      
      if (rowData && 
          rowData.name === userData.name &&
          rowData.username === userData.username) {
        return true;
      }
    }
    return false;
  }
  
  /**
   * Apply status filter
   */
  async applyStatusFilter(status) {
    await this.selectOption(this.selectors.statusFilter, status);
    await this.page.waitForTimeout(2000);
  }
  
  /**
   * Apply work unit filter
   */
  async applyWorkunitFilter(workunitId) {
    await this.selectOption(this.selectors.workunitFilter, workunitId);
    await this.page.waitForTimeout(2000);
  }
  
  /**
   * Test user form validation
   */
  async testFormValidation() {
    await this.clickAddUser();
    
    // Try to submit empty form
    await this.submitUser();
    
    // Check for validation messages
    const hasValidationErrors = await this.isVisible('.invalid-feedback') || 
                               await this.isVisible('.alert-danger');
    
    return hasValidationErrors;
  }
  
  /**
   * Test username uniqueness
   */
  async testUsernameUniqueness(existingUsername) {
    const testData = this.helpers.generateTestData().user;
    testData.username = existingUsername;
    
    await this.createUser(testData);
    
    // Check for duplicate username error
    const hasError = await this.isVisible('.alert-danger');
    
    if (hasError) {
      const errorText = await this.getText('.alert-danger');
      return errorText.includes('username') || errorText.includes('sudah digunakan');
    }
    
    return false;
  }
  
  /**
   * Test password validation
   */
  async testPasswordValidation() {
    await this.clickAddUser();
    
    const weakPasswords = ['123', 'abc', '12345'];
    const results = [];
    
    for (const password of weakPasswords) {
      await this.fill(this.selectors.passwordInput, password);
      await this.submitUser();
      
      const hasError = await this.isVisible('.invalid-feedback') || 
                      await this.isVisible('.alert-danger');
      
      results.push({
        password,
        rejected: hasError
      });
      
      // Clear the field
      await this.fill(this.selectors.passwordInput, '');
    }
    
    return results;
  }
  
  /**
   * Test phone number validation
   */
  async testPhoneValidation() {
    await this.clickAddUser();
    
    const invalidPhones = ['123', 'abc123', '12345678901234567890'];
    const results = [];
    
    for (const phone of invalidPhones) {
      await this.fill(this.selectors.phoneInput, phone);
      await this.submitUser();
      
      const hasError = await this.isVisible('.invalid-feedback') || 
                      await this.isVisible('.alert-danger');
      
      results.push({
        phone,
        rejected: hasError
      });
      
      // Clear the field
      await this.fill(this.selectors.phoneInput, '');
    }
    
    return results;
  }
  
  /**
   * Test user CRUD operations
   */
  async testCRUDOperations(testData) {
    const results = {
      create: false,
      read: false,
      update: false,
      delete: false
    };
    
    try {
      // Create
      await this.createUser(testData);
      results.create = await this.verifyUserInTable(testData);
      
      if (results.create) {
        // Read
        results.read = await this.searchUserByName(testData.name);
        
        // Update
        const updatedData = { ...testData, name: testData.name + ' Updated' };
        await this.editUser(0, updatedData);
        results.update = await this.verifyUserInTable(updatedData);
        
        // Delete
        await this.deleteUser(0);
        results.delete = !(await this.verifyUserInTable(updatedData));
      }
    } catch (error) {
      console.error('CRUD test error:', error);
    }
    
    return results;
  }
  
  /**
   * Verify user access control
   */
  async verifyUserAccessControl() {
    // Check if users can only see data from their own village/unit
    const userCount = await this.getUserCount();
    
    if (userCount > 0) {
      // Get first user's work unit
      const firstUserData = await this.getUserData(0);
      
      // Check if all users belong to the same work unit (for BUMDes User role)
      let allSameWorkunit = true;
      for (let i = 1; i < userCount; i++) {
        const userData = await this.getUserData(i);
        if (userData.workunit !== firstUserData.workunit) {
          allSameWorkunit = false;
          break;
        }
      }
      
      return {
        hasAccessControl: allSameWorkunit,
        userCount,
        workunit: firstUserData.workunit
      };
    }
    
    return {
      hasAccessControl: true,
      userCount: 0,
      workunit: null
    };
  }
}

module.exports = UserManagementPage;
