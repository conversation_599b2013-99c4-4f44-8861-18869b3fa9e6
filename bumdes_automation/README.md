# BUMDes Automation QA

Automation QA testing suite for BUMDes system using Playwright.

## Overview

This project contains automated tests for the BUMDes (village-owned enterprises) management system. The tests cover all major features of the application, including:

- Authentication (login, logout)
- Dashboard functionality
- Transaction management
- User management
- Beginning balance management
- Reports generation
- Master data management
- Registration process

## Project Structure

```
bumdes_automation/
├── package.json           # Project dependencies and scripts
├── playwright.config.js   # Playwright configuration
├── .env.example           # Example environment variables
├── utils/                 # Utility functions and helpers
│   ├── config.js          # Configuration settings
│   └── helpers.js         # Test helper functions
├── pages/                 # Page Object Models
│   ├── BasePage.js        # Base page with common methods
│   ├── LoginPage.js       # Login page object
│   ├── DashboardPage.js   # Dashboard page object
│   ├── TransactionPage.js # Transaction page object
│   └── ...                # Other page objects
└── tests/                 # Test files organized by feature
    ├── auth/              # Authentication tests
    ├── dashboard/         # Dashboard tests
    ├── transaction/       # Transaction tests
    ├── user/              # User management tests
    ├── reports/           # Reports tests
    ├── master/            # Master data tests
    ├── beginning-balance/ # Beginning balance tests
    └── registration/      # Registration tests
```

## Setup

### Quick Setup (Recommended)

1. Navigate to the bumdes_automation directory:
   ```bash
   cd bumdes_automation
   ```

2. Run the setup script:
   ```bash
   npm run setup
   ```

This will automatically:
- Install all dependencies
- Install Playwright browsers
- Create .env file from template
- Create necessary directories

### Manual Setup

If you prefer manual setup:

1. Install dependencies:
   ```bash
   npm install
   ```

2. Install Playwright browsers:
   ```bash
   npm run install-browsers
   ```

3. Copy `.env.example` to `.env` and update with your environment settings:
   ```bash
   cp .env.example .env
   ```

### Verify Setup

Run the setup verification test:
```bash
npx playwright test tests/setup-verification.test.js
```

This will verify that:
- Test data generation works
- Configuration is loaded correctly
- Helper functions work
- Application is accessible
- Browser capabilities are working

## Running Tests

### Run all tests:
```
npm test
```

### Run tests with UI mode:
```
npm run test:ui
```

### Run tests with headed browsers:
```
npm run test:headed
```

### Run specific test suites:
```
npm run test:auth
npm run test:dashboard
npm run test:transaction
npm run test:user
npm run test:report-feature
npm run test:master
npm run test:login          # Test login with SweetAlert handling
npm run test:quick          # Quick verification tests
npm run test:verify         # Setup verification tests
```

### Run tests by tag:
```
npm run test:smoke
npm run test:regression
```

## Test Categories

Tests are categorized using tags:

- `@smoke`: Basic functionality tests that verify critical paths
- `@regression`: More comprehensive tests for feature completeness
- `@validation`: Form validation and data validation tests
- `@access-control`: Tests for user role permissions and access control
- `@performance`: Tests for page load times and operation performance
- `@security`: Tests for security vulnerabilities
- `@error-handling`: Tests for proper error handling
- `@mobile`: Tests for mobile responsiveness
- `@feature`: Tests for specific feature functionality

## Page Objects

The tests use the Page Object Model pattern to encapsulate page functionality:

- `BasePage`: Common methods for all pages including SweetAlert handling
- `LoginPage`: Login functionality with automatic SweetAlert handling
- `DashboardPage`: Dashboard functionality
- `TransactionPage`: Transaction management
- `UserManagementPage`: User management
- `BeginningBalancePage`: Beginning balance management
- And more...

### SweetAlert Handling

The automation framework automatically handles SweetAlert popups that appear during various operations:

- **Login Flow**: Automatically handles success SweetAlert after login
- **Form Submissions**: Can handle confirmation and error SweetAlerts
- **CRUD Operations**: Handles success/error alerts for create, update, delete operations

```javascript
// Automatic handling in login
await loginPage.loginAsSuperAdmin(); // Handles SweetAlert automatically

// Manual SweetAlert handling
await page.handleSweetAlert('confirm'); // Click OK/Confirm
await page.handleSweetAlert('cancel');  // Click Cancel
await page.handleSweetAlert('close');   // Click Close
```

## Test Data

Test data is generated dynamically using the `TestHelpers` class, which provides methods for generating:

- Random user data
- Random business data
- Random transaction data
- Random report data
- Formatted currency and dates

## Configuration

The `config.js` file contains configuration settings for:

- Base URL
- Test user credentials
- Test data
- Selectors for UI elements
- URLs for different features

## Reporting

Test results are available in multiple formats:

- HTML report: `playwright-report/index.html`
- JSON report: `test-results/results.json`
- JUnit report: `test-results/results.xml`

To view the HTML report after running tests:
```
npm run test:report
```

## Debugging

For debugging tests:
```
npm run test:debug
```

## Cross-Browser Testing

Tests run on multiple browsers:
- Chromium
- Firefox
- WebKit (Safari)
- Mobile Chrome
- Mobile Safari

## Mobile Testing

Tests include mobile viewport testing for responsive design verification.

## Contributing

1. Create a feature branch
2. Add or update tests
3. Run tests to ensure they pass
4. Submit a pull request

## License

MIT
